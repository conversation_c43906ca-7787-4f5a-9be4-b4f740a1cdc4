{"__meta": {"id": "Xd82f394e494541e184b733c60acfaca0", "datetime": "2025-07-29 05:57:59", "utime": **********.410637, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753768678.523364, "end": **********.410666, "duration": 0.8873019218444824, "duration_str": "887ms", "measures": [{"label": "Booting", "start": 1753768678.523364, "relative_start": 0, "end": **********.339757, "relative_end": **********.339757, "duration": 0.8163928985595703, "duration_str": "816ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.339805, "relative_start": 0.****************, "end": **********.410678, "relative_end": 1.1920928955078125e-05, "duration": 0.*****************, "duration_str": "70.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "awutrfnokrtdbizzG4SFlFDGWaGSmBQVd8hSB8Bx", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-622505169 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-622505169\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1181743046 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1181743046\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1896361784 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1896361784\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-364028930 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-364028930\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1975338470 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1975338470\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1723108395 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:57:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZlQXE2YVNNSzVwVTIrTDhtQVFVRUE9PSIsInZhbHVlIjoiZzJWZjVvcW5WZGUyTE1weU9MRU4rMnRZRy9sNVRHU3N3VEE1QWdSY2VaM2cxMU94L1VMMEFrb1BFOC9BVGkvem94UGplaFJVNmFLTjhzZ2tIQU14aU10ZzduUDdoaHZXa1ZkLzZ4KzdaTXBMdGtKcU8xQjlhM2RQYzd3RFJqQ1ZaODJUUkU2bjR5aGxXU1FnOG1PMDFDaVNsV1NGRFRnOGFRN3RUMlVOK2NlN1hEUGxXTjkzMEJtcWE1emsxQVgzQjQ0N3BOTkVLam1mTmk1a2xpTVltZXJGZFZDeGpyYVJWUUd4NkhIU3BYVXBLTmZ5anVqUzRtNlpJOGtnUk1qSlJIM1JSZTlCVTlUT3duTllKRXQzb0dxOC8ybDJtS1ZrdkpXZVN2aUprVFNsdUR3OXBHVHF6a1NZakhSWGNGZi8yemltd09XOHN3VXNPRm16RXZOOW8rWDFGd3Z4KzNlazRCUnZxM3piWEhWeHNtTWN0Z0RkMlZjMDJRNmM3Mks2bjhYWXZlYmxYNXZQQ2F3WE1veFkxcjRINVNCNHdZcFlsUlZVbFdjRXhYQ1crODV6SzdhWWVRQTJEcjNLNHc5ZXJVTzhud09ycVBMcUp5ekNKL01pN0JiOXVxTVZEM2Nud09nWkNtTFFyS2xzMFFQbHNtSDgwaGFsUVcvajZxQ3IiLCJtYWMiOiI5N2I5ZDZiNzg1ZmZiYmYzNTA3OGMyM2I3YTcwZDg2ZGRmNzljN2MwYTFjYjExZjcyYTJlM2QxNTdhMzIyNzc3IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:57:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IndidUNpMmw3ajFiV2RDQkV0cFFGWVE9PSIsInZhbHVlIjoiMEE4LzJab3ZqcERSSXlIVk1OVUdNTmZyaGxDZGRrcjZCdWI0NEhpNno2bWFNVUZ5MkpyOHRLOUs2ZlBJTHV0RTN6bmNUdmRZU3JqNldqTHlnc21BQjhjRlhOWWk4SDk5dkpVeDZKbUd0STRMZWZsVHVjYklzV2RkL05wWEowK1drRTN6R0dWTTA2bGZtbVBGMDVYZlBac0FISnNOM2ZVRU52R3RnUjNPZmd4eDdsL3I5WFY3QVFDY3l3WVZsMzFJQkJ3VitvaHlNNFgxaXl0NHdtTXVTMUkwa2Z0cjhHNW1oaittZDFVOElJcy9JY3FpTWh1ZHYrWEc4WWpreDcyQmFXUFhuT3JMR3Z3SHhEeUpDcG1XNXFOL205RWhBeDJmejhaSjNkOWtzSFkrZUdNVGY1dXBBaXBGeXovT3Q4M2hBbFZzVm5LQWhvNTFocE1WemU3QzBMSDhSNkh6T2JJcFU2L2NpVU9pWWdjZitIQWJIMmpqaFBvTGdjMlhVVzJiZFUvcHRxUWFsZUpzdmhHWDlueHBnZ2svZjk5cFBkdzFIaFF5VGhPK3I3Ujl5NE1PdHUwSWpOQW5WVHBRQ1RPME1QTEtSMUU2eGpyRkxkWkF0TDlxWWdNbVZnSVBzMzY4cjdTZ0VSTlYzWXJwZTN3dmtlU1YrTkJvSVJMWCtIazUiLCJtYWMiOiJlNDA1ODk0ZGVlOTA5NTU0ZmQ2YTNmNDdiYzA4NWU0ZjljMTI5N2U0YzU3YWJkNDlkYjUyNTdmNjU5ODBjNjA3IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:57:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZlQXE2YVNNSzVwVTIrTDhtQVFVRUE9PSIsInZhbHVlIjoiZzJWZjVvcW5WZGUyTE1weU9MRU4rMnRZRy9sNVRHU3N3VEE1QWdSY2VaM2cxMU94L1VMMEFrb1BFOC9BVGkvem94UGplaFJVNmFLTjhzZ2tIQU14aU10ZzduUDdoaHZXa1ZkLzZ4KzdaTXBMdGtKcU8xQjlhM2RQYzd3RFJqQ1ZaODJUUkU2bjR5aGxXU1FnOG1PMDFDaVNsV1NGRFRnOGFRN3RUMlVOK2NlN1hEUGxXTjkzMEJtcWE1emsxQVgzQjQ0N3BOTkVLam1mTmk1a2xpTVltZXJGZFZDeGpyYVJWUUd4NkhIU3BYVXBLTmZ5anVqUzRtNlpJOGtnUk1qSlJIM1JSZTlCVTlUT3duTllKRXQzb0dxOC8ybDJtS1ZrdkpXZVN2aUprVFNsdUR3OXBHVHF6a1NZakhSWGNGZi8yemltd09XOHN3VXNPRm16RXZOOW8rWDFGd3Z4KzNlazRCUnZxM3piWEhWeHNtTWN0Z0RkMlZjMDJRNmM3Mks2bjhYWXZlYmxYNXZQQ2F3WE1veFkxcjRINVNCNHdZcFlsUlZVbFdjRXhYQ1crODV6SzdhWWVRQTJEcjNLNHc5ZXJVTzhud09ycVBMcUp5ekNKL01pN0JiOXVxTVZEM2Nud09nWkNtTFFyS2xzMFFQbHNtSDgwaGFsUVcvajZxQ3IiLCJtYWMiOiI5N2I5ZDZiNzg1ZmZiYmYzNTA3OGMyM2I3YTcwZDg2ZGRmNzljN2MwYTFjYjExZjcyYTJlM2QxNTdhMzIyNzc3IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:57:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IndidUNpMmw3ajFiV2RDQkV0cFFGWVE9PSIsInZhbHVlIjoiMEE4LzJab3ZqcERSSXlIVk1OVUdNTmZyaGxDZGRrcjZCdWI0NEhpNno2bWFNVUZ5MkpyOHRLOUs2ZlBJTHV0RTN6bmNUdmRZU3JqNldqTHlnc21BQjhjRlhOWWk4SDk5dkpVeDZKbUd0STRMZWZsVHVjYklzV2RkL05wWEowK1drRTN6R0dWTTA2bGZtbVBGMDVYZlBac0FISnNOM2ZVRU52R3RnUjNPZmd4eDdsL3I5WFY3QVFDY3l3WVZsMzFJQkJ3VitvaHlNNFgxaXl0NHdtTXVTMUkwa2Z0cjhHNW1oaittZDFVOElJcy9JY3FpTWh1ZHYrWEc4WWpreDcyQmFXUFhuT3JMR3Z3SHhEeUpDcG1XNXFOL205RWhBeDJmejhaSjNkOWtzSFkrZUdNVGY1dXBBaXBGeXovT3Q4M2hBbFZzVm5LQWhvNTFocE1WemU3QzBMSDhSNkh6T2JJcFU2L2NpVU9pWWdjZitIQWJIMmpqaFBvTGdjMlhVVzJiZFUvcHRxUWFsZUpzdmhHWDlueHBnZ2svZjk5cFBkdzFIaFF5VGhPK3I3Ujl5NE1PdHUwSWpOQW5WVHBRQ1RPME1QTEtSMUU2eGpyRkxkWkF0TDlxWWdNbVZnSVBzMzY4cjdTZ0VSTlYzWXJwZTN3dmtlU1YrTkJvSVJMWCtIazUiLCJtYWMiOiJlNDA1ODk0ZGVlOTA5NTU0ZmQ2YTNmNDdiYzA4NWU0ZjljMTI5N2U0YzU3YWJkNDlkYjUyNTdmNjU5ODBjNjA3IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:57:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1723108395\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1323500391 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">awutrfnokrtdbizzG4SFlFDGWaGSmBQVd8hSB8Bx</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1323500391\", {\"maxDepth\":0})</script>\n"}}