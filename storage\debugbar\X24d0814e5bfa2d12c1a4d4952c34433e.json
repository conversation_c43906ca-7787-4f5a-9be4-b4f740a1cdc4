{"__meta": {"id": "X24d0814e5bfa2d12c1a4d4952c34433e", "datetime": "2025-07-29 05:59:16", "utime": **********.249856, "method": "GET", "uri": "/calendar-events/available", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 3, "messages": [{"message": "[05:59:16] LOG.info: Getting available events for user: 84", "message_html": null, "is_string": false, "label": "info", "time": **********.216255, "xdebug_link": null, "collector": "log"}, {"message": "[05:59:16] LOG.info: Getting all future events", "message_html": null, "is_string": false, "label": "info", "time": **********.218417, "xdebug_link": null, "collector": "log"}, {"message": "[05:59:16] LOG.info: Found 7 events", "message_html": null, "is_string": false, "label": "info", "time": **********.228466, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753768755.170414, "end": **********.249907, "duration": 1.0794930458068848, "duration_str": "1.08s", "measures": [{"label": "Booting", "start": 1753768755.170414, "relative_start": 0, "end": **********.113344, "relative_end": **********.113344, "duration": 0.9429299831390381, "duration_str": "943ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.113356, "relative_start": 0.9429421424865723, "end": **********.249911, "relative_end": 4.0531158447265625e-06, "duration": 0.13655495643615723, "duration_str": "137ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46609104, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET calendar-events/available", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\CalendarEventController@getAvailableEvents", "as": "calendar-events.available", "namespace": null, "prefix": "/calendar-events", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FCalendarEventController.php&line=576\" onclick=\"\">app/Http/Controllers/CalendarEventController.php:576-649</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.021879999999999997, "accumulated_duration_str": "21.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.176822, "duration": 0.01479, "duration_str": "14.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 67.596}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.209903, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 67.596, "width_percent": 4.662}, {"sql": "select `id`, `title`, `duration`, `location`, `physical_address`, `meet_link` from `calendar_events` where `created_by` = 84 and `end_date` >= '2025-07-29 05:59:16' order by `start_date` asc", "type": "query", "params": [], "bindings": ["84", "2025-07-29 05:59:16"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/CalendarEventController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\CalendarEventController.php", "line": 600}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2188761, "duration": 0.00607, "duration_str": "6.07ms", "memory": 0, "memory_str": null, "filename": "CalendarEventController.php:600", "source": "app/Http/Controllers/CalendarEventController.php:600", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FCalendarEventController.php&line=600", "ajax": false, "filename": "CalendarEventController.php", "line": "600"}, "connection": "omx_sass_systam_db", "start_percent": 72.258, "width_percent": 27.742}]}, "models": {"data": {"App\\Models\\CalendarEvent": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCalendarEvent.php&line=1", "ajax": false, "filename": "CalendarEvent.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 8, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/calendar-events/available", "status_code": "<pre class=sf-dump id=sf-dump-1950430473 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1950430473\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-369513897 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-369513897\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-235262412 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-235262412\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1465825287 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6InpycHRPc1JOYkFQQzVtTFJJS0crOVE9PSIsInZhbHVlIjoiMmdmdmh1Zm1pTjJKbkxOZ3YvNzV3MzR1UjNzdUxFc3RDWXFDTUFOQVAvUnYzaUdCV3c1TlI2ZU1ZeENZaFdTYUd1NEpUMUpGTmRITFpiUkM0RG52SGpGQkd4OEhJYmZUS2Q3SXpxUitpdjRsMjhKdXozelM4czJKUG9rM1luN0M1K3N0Snpjb0s2Tm5yNE83ZjNLMkRxL1IvQkkzdUI5U1hpMlpMeVNwQmNoMWphbFFuZHJRMFZQV0xlZWtJZnQ0Szh3NFBNQklVZHZsYS9pK3lWRlltM2d3dVU1NHBBenQxZVdqQjlYVVB2UXF2bGV4dmFWMHNDalEyVkRoakhZdDdjNUlqTmZLYmUxRWhxdEI1Qll5YWRwWFJRaTlPbzZya2pscE85N2dzYlI1WjZYTFNjYU5LcDYrUkhydEtKazJuYk1XVG9KUG1kcU5zVXdHRTlhNkZRY3hPNGR0MUxCbFZleHpZNmM1ckVjc0k3bFZVWGhKUzNaVGNPTGdnZVBSMU4zVjh6NUFQN29wQm0yeEhGRW1pMk4zd0RZUkRKeGp5aGg0cTRSZEQvcUE3QTF2Y2w5WEVKS1I5U3BTaUhTWFB3WXUzWitDcy9MK0ZsRzY3d1pMQmdOblB1dHVzRmtCVXBhMkwwT1M5L3AwZHF4WDVpT3l0MGI0VGVhM0lZT2QiLCJtYWMiOiI4NzM2YmM0ODQ0N2YwODg3MzI5ZWY0NDg1MDgwY2VlOGZlM2M0YmIwYWUxYjE0NjI1MThiYzE2NjM5MmY5ZWExIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImtrYiswQ0RBRDVuN2tSZTJMaEpNc2c9PSIsInZhbHVlIjoiMlRWcFFEMjQyR1RtM1g0d0d4UUh5SUw1b1dydUFYL296NURRWE11b1owd1IvNittVklieU9JQjE2YlJoVytHTC91RS9NREFjUVV5REJOUmNnMDVyaHU5K0dDY29rQnU3YjZlWnBpSFd0YURpZk4zekZ1bSs1bGErQ2VUN2RvUkdTdEJod0tFT2Z3ZXk0aHkyTzgxWkhOUndBK2RkZmlYYzN4TzVNRmhLc3Q3ZXNvVTJxSUtXaG5VMzF5MmhTckh3UGVVcXExdUxjRXl6cTZIQUUrYU9vVTM0dmI3Y0ViTCtCaDJzM3NsT2tGTHJNQWVFYVBIejVKME5OQWtmazEwUjFMdzNjZ3crZzNCbUt2bEQydFZieWpBNngzbkhPRXcyNHpkeUlUaVhJZWVEa2lWTWg5Zll2TCtFVHZ1b1QyeC9VOE13OFhaTVg4eFBZSXhKSkFITU13dWhGWitONDVqRGFNWW5iU093b2xVeFBWTlNzTXpDZXU5aHJmVDJaTCtjK1lDRXhydXJvR1U0SVIwT0I5WVhjNC9oc1ZlSDFQLzJBQnRzcnlqLzdRVW1mRjAySzlzaFNGM3FySEJoTm5iZW5TUnIrNVRiZEhXaEthWGJaSmdwNUs1S2JjVDkzbVAzZG52cStIeTZ6NWJuYiswcDlwTFVVQlNJbEpNYWp0M0QiLCJtYWMiOiI4ZGIyZTVkZmVlNTc2MzliMDQ2YzUyZjhjOWY3NjMzZjg4MjRiYzM1MDU2MzI1Y2Q4ZGU4NmYyNjEzMDBiMzYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1465825287\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-606693149 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-606693149\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-845640370 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:59:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhJbFlWMTdSQ0hRWmViRVJKV1RUT2c9PSIsInZhbHVlIjoiOWlNZnkrYnd5Y2c3aEg5NEFqTUZpLzN0MlAzK1hqVW14SThOSXphVVZNaHIxWlFIN3QvWnAvYXdIci9jTFkzakpKcEZhbnZBaTY4R3MrcTBKNkNEV0NhYytsWGozdkY5RzgzejMxTStJZkYyZm5UVlhLQ0tXZXpBV3VJSkJzV0JmUEZRaVBITXJJbHdVbU9xdy9JeFgvSndTK0FrVHFpZDNYbW85eDJGWGQwZHFPVzd6US80Uy85L3EvR2ZZME9BSk9uKzUyQUtwVTBIdjBuZ1JIbWl1M2pHTzg5ay81Y3dDcFI5SVpCdHplQUNqRzQ5MnIxUHhWSHpCVWRaWmFiNkZJMTQ4RWFZSzN3VjFLaEVLTWNDWEFUYWxBcVRXRmtLL3JKbHFYY3R4c0djalNCaW9PNEFXK1JETVVwVUZkcmJuQUhlYjlzM0FjdTg3N0gzelhoMXVLcEFUQzNUVmFBMXJNbEJRQ2d5eVNmZCtoL2QvaEVPQldHUFFRcFF2T0N5ZUJ1L1pTemtUTVhNQmw3QjA0d1lCcnkvYWhCRDVYVnVpc0lkaUF4S1IxRlBkbTZBWllmTHlrNnZJUGUxd1JBSkJ5RUJEOHFLMS9VSTZWMU5DWU9qNDhmNFRvakxLN3pJT1BxVFppZ2NYMnJNcGI0NzdxMmFqOWZZN1ZYZWhrN2kiLCJtYWMiOiIxNzhkOGNlNjc5YTYzZGYxMmQ5ODc2NDg1YmJlYjc1MmM2M2FmODk3N2U1OGQ5NWJmYjFhNjQ5NzYzYzQxNzM0IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:59:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ii9uTTdDZ3VnM2ltbnQ0dk5KL3JyTUE9PSIsInZhbHVlIjoiUTBNeGRET1hXKzZuMGRDSWdFVkgrQnJLMVErNGFhWmpIckpYeGdFQVFWY0ZMUDJZOXQxUTRwSTJUZlBKSjNXeCtuSzQ4czBabTg2U2pHT2pGSkkwTUQ3RVFmVThSYTgwVUxwbHYxbzJMSXpmUXZvdm5TS2dHUU9qQVMxZkRGcUV2T1dESkZscUdCSUduODYrSXd3MGlSdVVaNjZFSXdnMDU5SmwzYjlTMFJjUmY3M0V0V2FaT3VKdFNtWXo5NHgwWHA2MUQ4czdQZDFQdXBDbVUzQWVmNTZDYzRHdXcwNEdLTjJWbU85blhUalpFNVM5dkJ0aDMxd01LRGRCTkdsQXIzRjgxdUR0STdxQ3I1WTN0aDNDRTY1UCtmQUlJbFQ0ZXl3VG42YWU2Z3REbEdQa3NreCtKdnFwWmQ4NVNaV2YzNnByOGd1eVJDczE0Q2p6WnRMODFiWVRwc3J4WXpweVBSeFJKUlJlS0ZlL1BWbUJBckcxTEp5TStSeU05UFpRWXBGNURTZG1neGhvejAyM1dJaVlWOTNmYUxoRFJVbTNjYjhCV2hPNEpjbHhMcVZWYlZlSzJmZldCU0dnY1Zqd2s0WVREZFBBM2orY3F1ajh6SUxjRnhTVUY1WmVYdGZzWEhLVDZZYzhVK2hiZ05VTUNEVlZETkFVM0U4Zk56bFoiLCJtYWMiOiJmY2ZhYThhYzlkNmYzNWJiYjk4ZWNmZmFhYjI5MWIzZTYyNTFiYmJmOTExYjYyYTg0YTE0OWEwNWY2OWRjNjBhIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:59:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhJbFlWMTdSQ0hRWmViRVJKV1RUT2c9PSIsInZhbHVlIjoiOWlNZnkrYnd5Y2c3aEg5NEFqTUZpLzN0MlAzK1hqVW14SThOSXphVVZNaHIxWlFIN3QvWnAvYXdIci9jTFkzakpKcEZhbnZBaTY4R3MrcTBKNkNEV0NhYytsWGozdkY5RzgzejMxTStJZkYyZm5UVlhLQ0tXZXpBV3VJSkJzV0JmUEZRaVBITXJJbHdVbU9xdy9JeFgvSndTK0FrVHFpZDNYbW85eDJGWGQwZHFPVzd6US80Uy85L3EvR2ZZME9BSk9uKzUyQUtwVTBIdjBuZ1JIbWl1M2pHTzg5ay81Y3dDcFI5SVpCdHplQUNqRzQ5MnIxUHhWSHpCVWRaWmFiNkZJMTQ4RWFZSzN3VjFLaEVLTWNDWEFUYWxBcVRXRmtLL3JKbHFYY3R4c0djalNCaW9PNEFXK1JETVVwVUZkcmJuQUhlYjlzM0FjdTg3N0gzelhoMXVLcEFUQzNUVmFBMXJNbEJRQ2d5eVNmZCtoL2QvaEVPQldHUFFRcFF2T0N5ZUJ1L1pTemtUTVhNQmw3QjA0d1lCcnkvYWhCRDVYVnVpc0lkaUF4S1IxRlBkbTZBWllmTHlrNnZJUGUxd1JBSkJ5RUJEOHFLMS9VSTZWMU5DWU9qNDhmNFRvakxLN3pJT1BxVFppZ2NYMnJNcGI0NzdxMmFqOWZZN1ZYZWhrN2kiLCJtYWMiOiIxNzhkOGNlNjc5YTYzZGYxMmQ5ODc2NDg1YmJlYjc1MmM2M2FmODk3N2U1OGQ5NWJmYjFhNjQ5NzYzYzQxNzM0IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:59:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ii9uTTdDZ3VnM2ltbnQ0dk5KL3JyTUE9PSIsInZhbHVlIjoiUTBNeGRET1hXKzZuMGRDSWdFVkgrQnJLMVErNGFhWmpIckpYeGdFQVFWY0ZMUDJZOXQxUTRwSTJUZlBKSjNXeCtuSzQ4czBabTg2U2pHT2pGSkkwTUQ3RVFmVThSYTgwVUxwbHYxbzJMSXpmUXZvdm5TS2dHUU9qQVMxZkRGcUV2T1dESkZscUdCSUduODYrSXd3MGlSdVVaNjZFSXdnMDU5SmwzYjlTMFJjUmY3M0V0V2FaT3VKdFNtWXo5NHgwWHA2MUQ4czdQZDFQdXBDbVUzQWVmNTZDYzRHdXcwNEdLTjJWbU85blhUalpFNVM5dkJ0aDMxd01LRGRCTkdsQXIzRjgxdUR0STdxQ3I1WTN0aDNDRTY1UCtmQUlJbFQ0ZXl3VG42YWU2Z3REbEdQa3NreCtKdnFwWmQ4NVNaV2YzNnByOGd1eVJDczE0Q2p6WnRMODFiWVRwc3J4WXpweVBSeFJKUlJlS0ZlL1BWbUJBckcxTEp5TStSeU05UFpRWXBGNURTZG1neGhvejAyM1dJaVlWOTNmYUxoRFJVbTNjYjhCV2hPNEpjbHhMcVZWYlZlSzJmZldCU0dnY1Zqd2s0WVREZFBBM2orY3F1ajh6SUxjRnhTVUY1WmVYdGZzWEhLVDZZYzhVK2hiZ05VTUNEVlZETkFVM0U4Zk56bFoiLCJtYWMiOiJmY2ZhYThhYzlkNmYzNWJiYjk4ZWNmZmFhYjI5MWIzZTYyNTFiYmJmOTExYjYyYTg0YTE0OWEwNWY2OWRjNjBhIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:59:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-845640370\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-747129622 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-747129622\", {\"maxDepth\":0})</script>\n"}}