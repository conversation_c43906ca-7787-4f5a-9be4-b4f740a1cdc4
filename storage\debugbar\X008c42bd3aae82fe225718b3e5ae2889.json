{"__meta": {"id": "X008c42bd3aae82fe225718b3e5ae2889", "datetime": "2025-07-29 05:57:16", "utime": **********.072714, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753768635.375088, "end": **********.072748, "duration": 0.697659969329834, "duration_str": "698ms", "measures": [{"label": "Booting", "start": 1753768635.375088, "relative_start": 0, "end": **********.007747, "relative_end": **********.007747, "duration": 0.6326589584350586, "duration_str": "633ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.007759, "relative_start": 0.****************, "end": **********.072761, "relative_end": 1.3113021850585938e-05, "duration": 0.****************, "duration_str": "65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9D3lUkWGq6mea2b01i86rnpQ7kMtYMnEwFBFTPS8", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1479104702 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1479104702\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1384041860 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1384041860\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-534400699 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-534400699\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-255116648 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-255116648\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-587179942 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-587179942\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:57:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBZd0ZSK0JZeC8vUm95VVBpUHNlWXc9PSIsInZhbHVlIjoibS9qWkNsb2hlaG5xdEJSU1hrTnh1OXY0N3h4RHd6UUdPbHk3TkV0cFExaHZGdGowcTF0UjdqM2VsS1ZXTVc5V0pUVFh4cU56VXdPVDBTY2doUFNVL0dFOGNRTGk2NVg0b3VYQWlVK1RXb3ZHRStub0x5MmZCZWVKZzk2VE5BRlJMZTE3MDVjL2ZzT2gwdzlsOEl0eE8wMlRvRUF0d094UFQrenloQm80aGNIMno0M093N2h4WG1tL2cwUnRuOE5PMEVOczBHczF1RjUxZnpDVmY5Vng2anRLbFJUaTNUKzIzS2xFVTV4U3h2TXFIV3JSWWZCc0x4Y0FNNXY3UVZXc2UzaDhlY0c3N01VL3IrK1I2K2h3TzZOUTgydlZkSTRXSlQraDRjMSs5K1R4M3ZPenBiRXMzRnppT2p1NDZOU0dheXFRK1ZLTG93QkNKVDJQeUVXMFlMVzgzeldIemp2U0tVWFZrV3llWGJreDVRYnlKK0EvcXZDWHQzWVE0cGNZSGgyQXNTTTJQc1Mrd2hSRXBGMFQ2OXZkOVJ4Vnl2dmFMa2xwRUQyVmp3RDVVQTlOOHVCekNjTVpoMkFlYUkxTG04Nm1hcmhIbEVKaTlReGJBRHlYOHhUQWhHYzJQK1pkR3phbjRTSE0zR1RSM0V5Uys4enQ2Z1pnTVFqVFNSeEEiLCJtYWMiOiI4MjQ0ZTYyMDhjYjk0NjEwYmRkODI1ZTg2ZTI4MmNjNDhhNzg0YmMzZGFjMWU4NjU2ZWZjYmUwN2VhNjAzNTliIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:57:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlZlVzNiVE5mNlhNdUJyK29zTzVROVE9PSIsInZhbHVlIjoiMWk5ZEZERTB1V3lPRGI0UEZBSXFiNFJWaks4blhYYVRrcDdHS3g2ZHRQWUczZE11enVyVjliL08zUU53RjZUWXoybm4wR3ZKMG9BclRXQVpYcWl1NkdidGU2RjRnbmNsWUE3d1NVcnd4VkxsbEZLRFU4WVNQQ3ZKb25jVUZWd1hXZEF4TmhPZ2I0L1ZneW8yaDI2aEMybnBwZS90aVpGeWtSaFhZN1p5UGRWdUxVY3hoV1pPaTdiWTJIMFVrWlN3ZUpQV2pyTFFUSVJEUnBGKzlFOUhXUlNqV01LOFo1MFpmQXVMZ3VDckpHQnJLQmd5ZUpuOHlHRldHQTFnVHBxWHg4dWdiejV2YitMR1NTWnhSakhZdzNKZmRoMm9WeVo4c1FsUXVZMzQ1eng3YTc5b2dpVzg2RjhUYWYzSG8yWnZmdDZUdTNBSW83MlBkQUM3M3pXNVcyRW9sMzR5SWc4TVZqMS9IZFc0MTMwTzk0Sm9hVE1jVUFzNCtOQjdPNTcxWnRsL3Q4WWttSW1MQ0RSWlpDQkpBSGo3ZzlzaDhkNFVYSHFDdFF1M2FZUXhtRmtwN3N3Y2FzUGtVY0VaVzN2ZDZJazlEUWJKZ0E0RmcrVmUyNHQxZE9lWFZCdDVadXE4WjljQ3pjRm5YSHNDUWltT0pBdDl2bnMrOVNCa0YyVFUiLCJtYWMiOiIwODEyYjhiYWRmOGU4NWU5NTg5NWFlNzg0NTZjMDk3M2FjYWU0ZjMwMTU5NTMxMDg3NzA0NjJmMzdlZGExMzUyIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:57:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBZd0ZSK0JZeC8vUm95VVBpUHNlWXc9PSIsInZhbHVlIjoibS9qWkNsb2hlaG5xdEJSU1hrTnh1OXY0N3h4RHd6UUdPbHk3TkV0cFExaHZGdGowcTF0UjdqM2VsS1ZXTVc5V0pUVFh4cU56VXdPVDBTY2doUFNVL0dFOGNRTGk2NVg0b3VYQWlVK1RXb3ZHRStub0x5MmZCZWVKZzk2VE5BRlJMZTE3MDVjL2ZzT2gwdzlsOEl0eE8wMlRvRUF0d094UFQrenloQm80aGNIMno0M093N2h4WG1tL2cwUnRuOE5PMEVOczBHczF1RjUxZnpDVmY5Vng2anRLbFJUaTNUKzIzS2xFVTV4U3h2TXFIV3JSWWZCc0x4Y0FNNXY3UVZXc2UzaDhlY0c3N01VL3IrK1I2K2h3TzZOUTgydlZkSTRXSlQraDRjMSs5K1R4M3ZPenBiRXMzRnppT2p1NDZOU0dheXFRK1ZLTG93QkNKVDJQeUVXMFlMVzgzeldIemp2U0tVWFZrV3llWGJreDVRYnlKK0EvcXZDWHQzWVE0cGNZSGgyQXNTTTJQc1Mrd2hSRXBGMFQ2OXZkOVJ4Vnl2dmFMa2xwRUQyVmp3RDVVQTlOOHVCekNjTVpoMkFlYUkxTG04Nm1hcmhIbEVKaTlReGJBRHlYOHhUQWhHYzJQK1pkR3phbjRTSE0zR1RSM0V5Uys4enQ2Z1pnTVFqVFNSeEEiLCJtYWMiOiI4MjQ0ZTYyMDhjYjk0NjEwYmRkODI1ZTg2ZTI4MmNjNDhhNzg0YmMzZGFjMWU4NjU2ZWZjYmUwN2VhNjAzNTliIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:57:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlZlVzNiVE5mNlhNdUJyK29zTzVROVE9PSIsInZhbHVlIjoiMWk5ZEZERTB1V3lPRGI0UEZBSXFiNFJWaks4blhYYVRrcDdHS3g2ZHRQWUczZE11enVyVjliL08zUU53RjZUWXoybm4wR3ZKMG9BclRXQVpYcWl1NkdidGU2RjRnbmNsWUE3d1NVcnd4VkxsbEZLRFU4WVNQQ3ZKb25jVUZWd1hXZEF4TmhPZ2I0L1ZneW8yaDI2aEMybnBwZS90aVpGeWtSaFhZN1p5UGRWdUxVY3hoV1pPaTdiWTJIMFVrWlN3ZUpQV2pyTFFUSVJEUnBGKzlFOUhXUlNqV01LOFo1MFpmQXVMZ3VDckpHQnJLQmd5ZUpuOHlHRldHQTFnVHBxWHg4dWdiejV2YitMR1NTWnhSakhZdzNKZmRoMm9WeVo4c1FsUXVZMzQ1eng3YTc5b2dpVzg2RjhUYWYzSG8yWnZmdDZUdTNBSW83MlBkQUM3M3pXNVcyRW9sMzR5SWc4TVZqMS9IZFc0MTMwTzk0Sm9hVE1jVUFzNCtOQjdPNTcxWnRsL3Q4WWttSW1MQ0RSWlpDQkpBSGo3ZzlzaDhkNFVYSHFDdFF1M2FZUXhtRmtwN3N3Y2FzUGtVY0VaVzN2ZDZJazlEUWJKZ0E0RmcrVmUyNHQxZE9lWFZCdDVadXE4WjljQ3pjRm5YSHNDUWltT0pBdDl2bnMrOVNCa0YyVFUiLCJtYWMiOiIwODEyYjhiYWRmOGU4NWU5NTg5NWFlNzg0NTZjMDk3M2FjYWU0ZjMwMTU5NTMxMDg3NzA0NjJmMzdlZGExMzUyIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:57:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9D3lUkWGq6mea2b01i86rnpQ7kMtYMnEwFBFTPS8</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}