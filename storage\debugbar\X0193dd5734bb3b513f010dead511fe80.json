{"__meta": {"id": "X0193dd5734bb3b513f010dead511fe80", "datetime": "2025-07-29 05:58:15", "utime": **********.707731, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753768694.855372, "end": **********.707773, "duration": 0.8524010181427002, "duration_str": "852ms", "measures": [{"label": "Booting", "start": 1753768694.855372, "relative_start": 0, "end": **********.63868, "relative_end": **********.63868, "duration": 0.7833080291748047, "duration_str": "783ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.638694, "relative_start": 0.****************, "end": **********.707776, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "69.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pCM3YEXUlSiKG9YXOPwwyJ8ER2KwQc9DStJu7F2E", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1480518601 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1480518601\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1297062061 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1297062061\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1529855019 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1529855019\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1992262242 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1992262242\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1132751206 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1132751206\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1578717678 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:58:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkluODVTa2JQZUZzcUhGcFEyUkNqR3c9PSIsInZhbHVlIjoibTVxeklveHA4M3Zoa3FCcjdtNUxuNGVUSUxDT2lzSmIvbStQQjAzcUJvTEhOQU5UOUQ1T0J1REJSTWc1NmNzT0NCTHdZSCs0K0FZbS9qSC9PUWxmck9vTmJMdmZJZVgvT1lUOXR0UmRFK0gzSkdtZmhyRC91SVZQN0FDZ0NYbnRkWnRWbWdsRW4yUkhkSm9seVVGVEptZVEwYUY2VDRnc1NGQzcrMjlrSFZLcW45Nm14YUw3ZzNUYUk2UGY5WHNMUVllU1FnTGthQjR6MFdqT3RmL0VudTJSRU92UVh5TythbGlLa1lxeUVKcUJMK1lzSTRvbmwxQ01tdmdQbGxmeHNZZGwyeEVBOE9GYS9xcHFoMnNDRWhoSkthMWtiMUlJQ2RaMFpCQVNkQ1Z1aGIrRHFuZU0yOVJmZmZEam1IbGVxOUFRdndOdy96N1NCMlg3MGx6VEJmVFdGYXlBNnFLRmxreUczZEU0MXdYMDl0MStteE14L29yREE2V0dKcFFCV0Vycmt4VFE3L1NmTit3RUcwR3NSUWtzSHZ2ZC9vNjVROVA4MFQrUWw2U3NIbitTMENyTDYvMTBZYTNwRzkxb3JmT2tvTUtDdHE0bnNsNDRUL1duOFc4a3NEaFliVElxZEMxVzEvNkVCSVZyZFpXUjJnOW43aE5MelJSNUQ1M3MiLCJtYWMiOiIzMDllNzM2ZGEwMTcyNTJkMzAwNmE1NTE4NDI0ODgzZTNmOWQ3ODRkMWRkZDBjODA5MDUyODUyZWExY2NmZDI3IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:58:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjJqc1MwQW1kaGdKWHVlbXZPUVFVT3c9PSIsInZhbHVlIjoibG5sd1U2TkJJMGZRdzlZYW52UytsWE1XdzNwTUFQdTBWNVBBa1p6SHJpRWlXdkZmM3Frc2h1dG5qNkxYQ0RCVFJTU25aeXFmb3NqNGNzdWFZc3UwTnVwK1BYRUtkazZHKy9wOElHU1lUQW10VEJtRUV0S3NQMk11SXN6eTNNZmdYekJMTERkZ0ZOWnpDaXFOa3ppL293Ym9kMW52cnoxUjI5aG9vRGVWdlE1RDZUa2c1NCszaUNMR0NQcUhGejYzUW5jNDZzY3B4UnB4dmswQjFFbHgyVlNXYXZLVHZwc0wvYlpYc0wwMWlLWmc2cVJQL0I0YkhSMzBzTk9wQTVmakthOExqVjNSUlhFa012ajlPci9CSktIL2w4NHlXQnFmcWcwTHMveE8wb2VmMFloakxyVDRyWkwxM1pDWXJwckkzWW9ENTU1QWhZNStZeEszWVF4V0g1aUZSN2E4MDZHcFZtZVo5QUlRclRsL3pwNGViSVphTThBblBZVmR1RHczanNCdDBWZG5DYXFWMXhSenZDSzZIYmxweGI0VllaVTRSSGNqcFcyZ3Bjc2lsTG9acDJCc2ZxSFltNkJJNFQrTjdrNnpFZjgwRVFuaVBHOU5LbTlTcW5ESUpBc1ZWbkk1SEVkd0Z2OHpycjFJQWdJNDA1MHZIVUU3aDZSeVdVVTYiLCJtYWMiOiIyZWQ1NTQxN2FhMzMxYzQ1NmJjNmU5MDcwMzNlOWYyNzhlYzU1ZmMyNTA3Y2U2MDgxMzIzZDQ2MGZlZjQyNjM1IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:58:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkluODVTa2JQZUZzcUhGcFEyUkNqR3c9PSIsInZhbHVlIjoibTVxeklveHA4M3Zoa3FCcjdtNUxuNGVUSUxDT2lzSmIvbStQQjAzcUJvTEhOQU5UOUQ1T0J1REJSTWc1NmNzT0NCTHdZSCs0K0FZbS9qSC9PUWxmck9vTmJMdmZJZVgvT1lUOXR0UmRFK0gzSkdtZmhyRC91SVZQN0FDZ0NYbnRkWnRWbWdsRW4yUkhkSm9seVVGVEptZVEwYUY2VDRnc1NGQzcrMjlrSFZLcW45Nm14YUw3ZzNUYUk2UGY5WHNMUVllU1FnTGthQjR6MFdqT3RmL0VudTJSRU92UVh5TythbGlLa1lxeUVKcUJMK1lzSTRvbmwxQ01tdmdQbGxmeHNZZGwyeEVBOE9GYS9xcHFoMnNDRWhoSkthMWtiMUlJQ2RaMFpCQVNkQ1Z1aGIrRHFuZU0yOVJmZmZEam1IbGVxOUFRdndOdy96N1NCMlg3MGx6VEJmVFdGYXlBNnFLRmxreUczZEU0MXdYMDl0MStteE14L29yREE2V0dKcFFCV0Vycmt4VFE3L1NmTit3RUcwR3NSUWtzSHZ2ZC9vNjVROVA4MFQrUWw2U3NIbitTMENyTDYvMTBZYTNwRzkxb3JmT2tvTUtDdHE0bnNsNDRUL1duOFc4a3NEaFliVElxZEMxVzEvNkVCSVZyZFpXUjJnOW43aE5MelJSNUQ1M3MiLCJtYWMiOiIzMDllNzM2ZGEwMTcyNTJkMzAwNmE1NTE4NDI0ODgzZTNmOWQ3ODRkMWRkZDBjODA5MDUyODUyZWExY2NmZDI3IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:58:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjJqc1MwQW1kaGdKWHVlbXZPUVFVT3c9PSIsInZhbHVlIjoibG5sd1U2TkJJMGZRdzlZYW52UytsWE1XdzNwTUFQdTBWNVBBa1p6SHJpRWlXdkZmM3Frc2h1dG5qNkxYQ0RCVFJTU25aeXFmb3NqNGNzdWFZc3UwTnVwK1BYRUtkazZHKy9wOElHU1lUQW10VEJtRUV0S3NQMk11SXN6eTNNZmdYekJMTERkZ0ZOWnpDaXFOa3ppL293Ym9kMW52cnoxUjI5aG9vRGVWdlE1RDZUa2c1NCszaUNMR0NQcUhGejYzUW5jNDZzY3B4UnB4dmswQjFFbHgyVlNXYXZLVHZwc0wvYlpYc0wwMWlLWmc2cVJQL0I0YkhSMzBzTk9wQTVmakthOExqVjNSUlhFa012ajlPci9CSktIL2w4NHlXQnFmcWcwTHMveE8wb2VmMFloakxyVDRyWkwxM1pDWXJwckkzWW9ENTU1QWhZNStZeEszWVF4V0g1aUZSN2E4MDZHcFZtZVo5QUlRclRsL3pwNGViSVphTThBblBZVmR1RHczanNCdDBWZG5DYXFWMXhSenZDSzZIYmxweGI0VllaVTRSSGNqcFcyZ3Bjc2lsTG9acDJCc2ZxSFltNkJJNFQrTjdrNnpFZjgwRVFuaVBHOU5LbTlTcW5ESUpBc1ZWbkk1SEVkd0Z2OHpycjFJQWdJNDA1MHZIVUU3aDZSeVdVVTYiLCJtYWMiOiIyZWQ1NTQxN2FhMzMxYzQ1NmJjNmU5MDcwMzNlOWYyNzhlYzU1ZmMyNTA3Y2U2MDgxMzIzZDQ2MGZlZjQyNjM1IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:58:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1578717678\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1028419052 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pCM3YEXUlSiKG9YXOPwwyJ8ER2KwQc9DStJu7F2E</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1028419052\", {\"maxDepth\":0})</script>\n"}}