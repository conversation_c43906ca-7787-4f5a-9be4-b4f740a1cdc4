{"__meta": {"id": "Xa64fdb90cf8ed2b86abd8e0af288e981", "datetime": "2025-07-29 05:58:55", "utime": **********.983044, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753768734.674788, "end": **********.983076, "duration": 1.3082880973815918, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": 1753768734.674788, "relative_start": 0, "end": **********.899971, "relative_end": **********.899971, "duration": 1.2251830101013184, "duration_str": "1.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.899986, "relative_start": 1.****************, "end": **********.983078, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "83.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "t5gg2pdpsQnV3MuBqnuNwlE7up5HCxuwxoRF1dlT", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-589746153 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-589746153\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1143284306 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1143284306\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-324487462 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-324487462\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-351733490 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-351733490\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-697898007 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-697898007\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-60633745 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:58:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkR5czY3REhsRHV1NmlHRldZOEZHRVE9PSIsInZhbHVlIjoiZmhFU1p5V2YydUUwenoyalExYlBzSUM0TWNkTEQ2ZFJPbTgyN3k0QjVvSWJzU2JDSzlaTW1ZMXdJN0trM21iR0xHRS9VV0k0czFoVmZVcmdzeE9uNVYzTE52dE1zMnRyUlpCaUtBMVpZSk1YTTBieDJHT25XOWhWSmJKOVFpb1NIb2tyTjZ4bzBISE5SYkR6MS9aWmd1Y0F0eFFRc1Z2QjQ4TU5Gd3NTMklCVGhZUXdNSjdvZEc1Mjg1eWovc0ZpbitZUGI4UzF3UTZpdDJvRm16MjZ3ekxkZ1orVmR6N1BML0JOdXlzdFNTUUgyTTd6WmprOGdQL2didTZqQUxJUXltalQwbXB3dGp3SjVXeW5WQUFCRDNWb2J6em9WSWYxa2tHalRGR2diT1cxeDhQckMzMXNDOElmWVlrN2tISWhCRE1yVEt1U2Z2b3hvZDJzTHVZTS9FckNEMmp1N2xuS2NaNkJSUlgzQys0TnZHODViWmc1Z28zanlTM2ZzbmVVT2drd3JFQnBDY2pnM29VSTNITmJ1M3lBTFZWZVo2TDM4Wmd0N1FVck1YeGdTZnlzMURzUkZXMHhzSXZ3eFM1blZyeVZsV3lqZ1V5R3JjeHhrUHZ0TDhHTGFJRHJVN3VHSUxWTWFreEdIL1p2akwvUEVoQlQxRXBMNGJEc2U2enciLCJtYWMiOiJiNjRiZmU1OTIwNzEyYjg5NzI3YjIxZmU4NWI2NGM3NDNiOWNhNDk2M2EyZTk1MzQ1YmUxY2VmMzI3MjE1OTg1IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:58:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ilh2dFJaZnZVN3JsTkJ0RWl2NCtialE9PSIsInZhbHVlIjoiMklxMWxyb0J3VTMya1RsR211d3hyeXFIK3ZBdHU0U3g4SitmUE4wOTVQK0c3b3EzRDhvZGdMTFl0Y0p2azV1S0JkN01FQWoyUUs4MkRGRlRwa0pjcTdDRmpFcUI1WWQ4L1MxWmhBSkVnVFZ0S0twYTN3eWhuREZXSTNBUVdQRWxha2N5N0xXbGFPeWFibThSQUZOWTh3QnZLNWlnTStNUkVTd29jVnBvZlNPdjBaYm1SbHVKRk90NUVOYmRFaHNBcEgzdTJQam9vWGNNSjFJZXNDb3pOOTlFckpjQlRnaFNoTldMVVlReWFLZVNJMDVBN0VOVXhJTVJFWUg2ZmwzckJjOXE4ZTczREJ2UDlMVTVTcitZbkYvUXVicmJ1R2RBQzBraGMrd3BnMW5CeW56RE1yRFg5QjdMTjA4QmhTR1gvOFk0T1o2SXRtS0Y2bHg3TTMyY3FrUmpBSzFlYXRSOE92S1JoVGdINmNvaEVVSGVZNmJCLzV4MFhubHFTdmxMaThZbGhXSzFtUDlETmo4dHFyVWZVdy9EeEQ5bnZWcEgwYXZCUmtMY3VQdk1sUm1nYk5zWDFOWGxBclpCeVEyaEFQeTVyc25jRjBoUXlWRlVVVExoTnczMWdvSlhxSGlER1EwZlE3d1NMY1hqVzJSQ0JKc3BkTnJpdjV6Z2xNMUgiLCJtYWMiOiI5MWQwNjMxNDEyMzRiZWM4MDRjMjY0YjA0YjVhM2RjYmI3MjJmMzQ3ZmZjYjJmODEzNWJiODI1OWZlNjk4ZmFmIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:58:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkR5czY3REhsRHV1NmlHRldZOEZHRVE9PSIsInZhbHVlIjoiZmhFU1p5V2YydUUwenoyalExYlBzSUM0TWNkTEQ2ZFJPbTgyN3k0QjVvSWJzU2JDSzlaTW1ZMXdJN0trM21iR0xHRS9VV0k0czFoVmZVcmdzeE9uNVYzTE52dE1zMnRyUlpCaUtBMVpZSk1YTTBieDJHT25XOWhWSmJKOVFpb1NIb2tyTjZ4bzBISE5SYkR6MS9aWmd1Y0F0eFFRc1Z2QjQ4TU5Gd3NTMklCVGhZUXdNSjdvZEc1Mjg1eWovc0ZpbitZUGI4UzF3UTZpdDJvRm16MjZ3ekxkZ1orVmR6N1BML0JOdXlzdFNTUUgyTTd6WmprOGdQL2didTZqQUxJUXltalQwbXB3dGp3SjVXeW5WQUFCRDNWb2J6em9WSWYxa2tHalRGR2diT1cxeDhQckMzMXNDOElmWVlrN2tISWhCRE1yVEt1U2Z2b3hvZDJzTHVZTS9FckNEMmp1N2xuS2NaNkJSUlgzQys0TnZHODViWmc1Z28zanlTM2ZzbmVVT2drd3JFQnBDY2pnM29VSTNITmJ1M3lBTFZWZVo2TDM4Wmd0N1FVck1YeGdTZnlzMURzUkZXMHhzSXZ3eFM1blZyeVZsV3lqZ1V5R3JjeHhrUHZ0TDhHTGFJRHJVN3VHSUxWTWFreEdIL1p2akwvUEVoQlQxRXBMNGJEc2U2enciLCJtYWMiOiJiNjRiZmU1OTIwNzEyYjg5NzI3YjIxZmU4NWI2NGM3NDNiOWNhNDk2M2EyZTk1MzQ1YmUxY2VmMzI3MjE1OTg1IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:58:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ilh2dFJaZnZVN3JsTkJ0RWl2NCtialE9PSIsInZhbHVlIjoiMklxMWxyb0J3VTMya1RsR211d3hyeXFIK3ZBdHU0U3g4SitmUE4wOTVQK0c3b3EzRDhvZGdMTFl0Y0p2azV1S0JkN01FQWoyUUs4MkRGRlRwa0pjcTdDRmpFcUI1WWQ4L1MxWmhBSkVnVFZ0S0twYTN3eWhuREZXSTNBUVdQRWxha2N5N0xXbGFPeWFibThSQUZOWTh3QnZLNWlnTStNUkVTd29jVnBvZlNPdjBaYm1SbHVKRk90NUVOYmRFaHNBcEgzdTJQam9vWGNNSjFJZXNDb3pOOTlFckpjQlRnaFNoTldMVVlReWFLZVNJMDVBN0VOVXhJTVJFWUg2ZmwzckJjOXE4ZTczREJ2UDlMVTVTcitZbkYvUXVicmJ1R2RBQzBraGMrd3BnMW5CeW56RE1yRFg5QjdMTjA4QmhTR1gvOFk0T1o2SXRtS0Y2bHg3TTMyY3FrUmpBSzFlYXRSOE92S1JoVGdINmNvaEVVSGVZNmJCLzV4MFhubHFTdmxMaThZbGhXSzFtUDlETmo4dHFyVWZVdy9EeEQ5bnZWcEgwYXZCUmtMY3VQdk1sUm1nYk5zWDFOWGxBclpCeVEyaEFQeTVyc25jRjBoUXlWRlVVVExoTnczMWdvSlhxSGlER1EwZlE3d1NMY1hqVzJSQ0JKc3BkTnJpdjV6Z2xNMUgiLCJtYWMiOiI5MWQwNjMxNDEyMzRiZWM4MDRjMjY0YjA0YjVhM2RjYmI3MjJmMzQ3ZmZjYjJmODEzNWJiODI1OWZlNjk4ZmFmIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:58:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-60633745\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1600446097 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">t5gg2pdpsQnV3MuBqnuNwlE7up5HCxuwxoRF1dlT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1600446097\", {\"maxDepth\":0})</script>\n"}}