{"__meta": {"id": "X23d11e16b84b39f489407032b61c5d00", "datetime": "2025-07-29 05:55:54", "utime": **********.769386, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[05:55:54] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 84,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.765362, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753768553.478412, "end": **********.769411, "duration": 1.290999174118042, "duration_str": "1.29s", "measures": [{"label": "Booting", "start": 1753768553.478412, "relative_start": 0, "end": **********.551917, "relative_end": **********.551917, "duration": 1.073505163192749, "duration_str": "1.07s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.551932, "relative_start": 1.0735201835632324, "end": **********.769419, "relative_end": 7.867813110351562e-06, "duration": 0.21748685836791992, "duration_str": "217ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48265224, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.03779999999999999, "accumulated_duration_str": "37.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6185129, "duration": 0.02934, "duration_str": "29.34ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 77.619}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.6646478, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 77.619, "width_percent": 2.011}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.670933, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "omx_sass_systam_db", "start_percent": 79.63, "width_percent": 2.381}, {"sql": "select * from `ch_favorites` where `user_id` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.675426, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "omx_sass_systam_db", "start_percent": 82.011, "width_percent": 2.222}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.697342, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "omx_sass_systam_db", "start_percent": 84.233, "width_percent": 2.434}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (84) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.708493, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 86.667, "width_percent": 2.328}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (84) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.715125, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 88.995, "width_percent": 2.196}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.721305, "duration": 0.00333, "duration_str": "3.33ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 91.19, "width_percent": 8.81}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 543, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 546, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1363475321 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1363475321\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-739297273 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-739297273\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1772247607 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1772247607\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1742932253 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjUwL3lMbFRzL0t3OW9LSDI0SjkwZ0E9PSIsInZhbHVlIjoiYlMyT2F6M1MrUitzQVFBd2t0cXpUNFNuMHR5cS9FTUdHWmhhRjUrbjFiWU11VGJuTVJqREg4TXRYSnlkcG43b1E1WXZudUJFb0cwUHUwMnRGakNlT2xuOE41RkU3SUtkMzlQOWQ3UTVIa0Z1ZjZYWExIWU5OYUxyS0NleUNMeVltWVFsTlZBN3VYUkp4ZVdsUDUrMUZXYzh4SHdaUGp6QlhuVVRLUTNkR1RIdGdVTWw1UWNJYzNObldrR1laNGhPTzM3TS9UM1BWbjNSUWJnNWxnTGlrN1dlS2dmeU1oS21wQkcyU01aYllxaUxacy96R1NuVXRNVXNwenZtMU96OTUzUHlEeUtycTlqWDdMcjhJOGl5cE1SNnp2RVdGMVRxUVNKendua0tkZkhKSXllaE9GWDRwUmJCQ0IxTFBOM2w5Z2hhTndSY252MXhtYXJpb0lUN1BIazJQU1JPamJmOGlVZHBIRmZKWis5S1pyUUFpTVdMT1R2b3AyNVEvRmNYQnpTcmFBNDkzdmtDQks3YmJYRjZEbjZDRGREVDRjeFhiK201VGYrUldoSzhmL0I2TVBaRDJNWlptQm9nVlFMMHdUZnk4QlY3NHdzY2pCZmVtVUZ6UGJieXp1b1dvUkoxSEMzdHIybGFBMlYrWWNmR1daK0lCMHRLY1o3Mi9YUTMiLCJtYWMiOiI3MWYyNjZhZGI4ZWY4MDEwNzJhNmI5Y2FiMjE1MWYxYTgzOWNlYTJjYTBkYjlhYTM1YzMxMDU1MGI5NjE0Yjg3IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkozTjFqU0F5SjRZLzA5QXhsTmovelE9PSIsInZhbHVlIjoiWVNtdlJOWi9jZGErSGJyZmZ6S1hvN0R5RDZibm94N09NTE16N2cwVW5JaW9QeDVxQlNrUFBXV2hlaFZESjlzTkh1djBUelJGTnNnZ1JNWTR5NE82dlJTaThUaXZ2Qzd2STlBVEdKajAzbi9rT0U4QzFuUHpJbGFZY1kwWnZKSXRJd05vQ1dzMnJHNWQwTmNNaXpXNjcrc3lFeVJuK2NuZlN5VWhHSDFoYWJNNGRmZVVQNTA0QXVEeTVtZExzMkJMY3p5L3l5VnNuZkhXMWxaUkJxQ0hnUVZUbjBBUFV0U1VJRlJjV1o5S25aUGgvZ054SEQ5Yzk1d2lxNXhYQ0FNUWxxMVE1MTVQZldIajV4dTVjTVFTMEVYZ0ZRT2YzQ016NE9ENXQrZ1BNbkVLcG00Yi9kR3lTMW0zM0h4WEFqNVNQTXh2aktOSEJ3VU9NMkc4Qi9EbGNXMVBuUHdkd2wvNm82Sm44UTlMNEdFalIrbU94dEVHaUtrOExSODczM3pSMWlVSGh1ekM1ZUlqVkhOM2Q5OTc0ekFVVFlwL0hjdVhBaHVEZ2VHZTk4V2tCaGErUnk3SjhPU3B5RW5aOUlZcFFnM25pWHd3aE9oaXZncE1kSm8vSjVLZTBLQktvRlI1dUY1eVNBYzZKN2FHMVpvNGNGdFVLUkhMYlp5T1BGdEoiLCJtYWMiOiI5MmVlMzAxMjZjZTZkOWZkYjQxMTAzM2M0OGIzMDM5OGViODY3ZWM1M2NkMjlkNjNhOTIwYzJhNThkOWJmZjUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1742932253\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1584940304 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1584940304\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-500739565 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:55:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklERWFNTnBnZG9TVGdnOVE1dnhsUkE9PSIsInZhbHVlIjoiYVBXNHFiUElQUVRPUkJRTThQbm9oUmlPS1ZjKytDYjBoa2kvdVF5OHMwY20rck9KZEZWRStBazRBMjk3ckVSMG90RGYwSlMxd2c1UXVSRGxha0Z4QS8yQUVLbHFTWkxiUWJSZnhFTHN5bkdzQUZOSHhWREwxYzhoR0E4VEpQUjkrOEVhWVZ0YXhncU5QUTUrZGRjbjFxYTcxZ3h0NnMzYTI5S3ZsZzZMRVp0bUkrTmpzNEFJNndNdzc4c2RSeklLcTRGSUpvSGFuQ2dxTEswUGZSZzZZK011Nk9VOGI3eElZRDRPUW5OOUV2WTBsSzg1YnU4bmFpTkhOTVVlNjlJSTQ3UTZ0UlBlaHkrS091bDV2ZjNGVDFkeW9YNFlJcHRsN2pWN213Y1gvTkUweE45aGJqUzBXSVFpb1JvNDhhczdrNXAvK0dkdFNjMHl1NHpPSnYwaEtlU0Q3czVpaEQ5Wms0bzlqMHhjWlVMbC9pbVR1WklNL1BsZ2J6Y1RxUHBDNmh3eXF4cUc0Q3lXaWMrb1AvS25NNmp1YngzQlRjQ28yTlgvektabFRyZ0lBWG9CV09zaS85ZWkzbkJ2TWl6SmdGa1ZhOWVNcjVzbk14RzVsUGhhNHZDRGw1UVpXR29HU3dkckg3UGlZNGp3WXBzT3paUGs4dFpnbGRVcG1OWGQiLCJtYWMiOiJlZGQzMTFjMWU4N2E4M2QzZDFlMDc2MGU1NGE0MzU2NTlmOWYwNjViNzkxN2MwMGFmYmRhMzBhMTJiNDhiYjY0IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:55:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IktBM3JvR05ZZjBESlBTb0Y5dnUxdFE9PSIsInZhbHVlIjoid3djb0Uxb1lqU2dISjEvL1QzajJxM3RWQW5FMHEycDVvUFdpMmpQYlB5Q0YrQzZEUC9aNHg1bmlaU3MxYUtKemhiVlVaQU5KZ0cwbHdlTk90LzdsZ24wT2VBZkVjVEZibmtqMGduS25WdnZaZHhWanRvM2k4c0ljNVpQUlE5bmo4clB4VGRLakk4MmJBWi80QWdqQlhiNXZ1T1BWTkNSYWhVYlJMT3VUUUhKaDg1N0RHa094alYxSm9PTEMydHo0a3JtdllPNGpVdVVXUDd0RXpJSkJhS0tSc0ltOU5FYm05L2Rqd0tXOHByV0FRaVNSSGdESUVVZnU0K3Fxcko0cUhueGRUcXZpR0ZqNFNsWnJJY2JKRHA0Q2JZTXRDRDFRajI2WXpqZnZUL1RCaEVoTGNNUSt6ejlMZHpEM0h6dUpFcXJrc2I5NFZUbjBlLzFUbzdLRnZudDNxRXlySWw2amU4OTR5akt3UGVHRGRZcHkvelM3YmZHNk9FUXJESC81d1B0RjdQL2NLclZDUUc5QkFxQ2VkQWpDQUlqcVhtc0MzVVAzVllTcE8wbEo2eVRIK3VteFFpa2VyN29BS2VPM00xdmpDNHlFeHVnUlIza2lPQXdvVWRQNDVCNFRlQTRpZm1NdnhXc0tKREEzaWdKNk9aendkeGJGOVdnM09rRTgiLCJtYWMiOiJjMTk1MDVkMzQ1YTY1N2E3MDdkNzZlZjY4YWY4MGNlMjA0MzA5NjgyYmJjNGUxYjMxNTJmMmMxZTc5MWRmMjk5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:55:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklERWFNTnBnZG9TVGdnOVE1dnhsUkE9PSIsInZhbHVlIjoiYVBXNHFiUElQUVRPUkJRTThQbm9oUmlPS1ZjKytDYjBoa2kvdVF5OHMwY20rck9KZEZWRStBazRBMjk3ckVSMG90RGYwSlMxd2c1UXVSRGxha0Z4QS8yQUVLbHFTWkxiUWJSZnhFTHN5bkdzQUZOSHhWREwxYzhoR0E4VEpQUjkrOEVhWVZ0YXhncU5QUTUrZGRjbjFxYTcxZ3h0NnMzYTI5S3ZsZzZMRVp0bUkrTmpzNEFJNndNdzc4c2RSeklLcTRGSUpvSGFuQ2dxTEswUGZSZzZZK011Nk9VOGI3eElZRDRPUW5OOUV2WTBsSzg1YnU4bmFpTkhOTVVlNjlJSTQ3UTZ0UlBlaHkrS091bDV2ZjNGVDFkeW9YNFlJcHRsN2pWN213Y1gvTkUweE45aGJqUzBXSVFpb1JvNDhhczdrNXAvK0dkdFNjMHl1NHpPSnYwaEtlU0Q3czVpaEQ5Wms0bzlqMHhjWlVMbC9pbVR1WklNL1BsZ2J6Y1RxUHBDNmh3eXF4cUc0Q3lXaWMrb1AvS25NNmp1YngzQlRjQ28yTlgvektabFRyZ0lBWG9CV09zaS85ZWkzbkJ2TWl6SmdGa1ZhOWVNcjVzbk14RzVsUGhhNHZDRGw1UVpXR29HU3dkckg3UGlZNGp3WXBzT3paUGs4dFpnbGRVcG1OWGQiLCJtYWMiOiJlZGQzMTFjMWU4N2E4M2QzZDFlMDc2MGU1NGE0MzU2NTlmOWYwNjViNzkxN2MwMGFmYmRhMzBhMTJiNDhiYjY0IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:55:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IktBM3JvR05ZZjBESlBTb0Y5dnUxdFE9PSIsInZhbHVlIjoid3djb0Uxb1lqU2dISjEvL1QzajJxM3RWQW5FMHEycDVvUFdpMmpQYlB5Q0YrQzZEUC9aNHg1bmlaU3MxYUtKemhiVlVaQU5KZ0cwbHdlTk90LzdsZ24wT2VBZkVjVEZibmtqMGduS25WdnZaZHhWanRvM2k4c0ljNVpQUlE5bmo4clB4VGRLakk4MmJBWi80QWdqQlhiNXZ1T1BWTkNSYWhVYlJMT3VUUUhKaDg1N0RHa094alYxSm9PTEMydHo0a3JtdllPNGpVdVVXUDd0RXpJSkJhS0tSc0ltOU5FYm05L2Rqd0tXOHByV0FRaVNSSGdESUVVZnU0K3Fxcko0cUhueGRUcXZpR0ZqNFNsWnJJY2JKRHA0Q2JZTXRDRDFRajI2WXpqZnZUL1RCaEVoTGNNUSt6ejlMZHpEM0h6dUpFcXJrc2I5NFZUbjBlLzFUbzdLRnZudDNxRXlySWw2amU4OTR5akt3UGVHRGRZcHkvelM3YmZHNk9FUXJESC81d1B0RjdQL2NLclZDUUc5QkFxQ2VkQWpDQUlqcVhtc0MzVVAzVllTcE8wbEo2eVRIK3VteFFpa2VyN29BS2VPM00xdmpDNHlFeHVnUlIza2lPQXdvVWRQNDVCNFRlQTRpZm1NdnhXc0tKREEzaWdKNk9aendkeGJGOVdnM09rRTgiLCJtYWMiOiJjMTk1MDVkMzQ1YTY1N2E3MDdkNzZlZjY4YWY4MGNlMjA0MzA5NjgyYmJjNGUxYjMxNTJmMmMxZTc5MWRmMjk5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:55:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-500739565\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1258751818 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1258751818\", {\"maxDepth\":0})</script>\n"}}