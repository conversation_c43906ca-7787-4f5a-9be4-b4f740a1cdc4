{"__meta": {"id": "X2b67151ce369a1a32ba43e0fa050f615", "datetime": "2025-07-29 05:58:30", "utime": **********.285538, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753768709.330231, "end": **********.285577, "duration": 0.9553461074829102, "duration_str": "955ms", "measures": [{"label": "Booting", "start": 1753768709.330231, "relative_start": 0, "end": **********.211719, "relative_end": **********.211719, "duration": 0.8814880847930908, "duration_str": "881ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.211736, "relative_start": 0.***************, "end": **********.28558, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "73.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Ua5BrbNDPybjMH5VC8eOstHjXjHoF5PPn8eQugo0", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-526776008 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-526776008\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-198387287 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-198387287\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-816592649 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-816592649\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1771601624 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1771601624\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1017272703 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1017272703\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-234496633 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:58:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNDWklHWDVXZ2QveXRTSGFBT3Avamc9PSIsInZhbHVlIjoiMVpZNW9mR1ZldEFNZE9zKzB0STdRdHN4dFVJMDU1S3RQRDVTeVNJRjFUNEZyb3NmZGxhQ1VFU0RldXZDZGtyblo5Y3ZqYXFPbWdDK2xGdlkwOEtmRFBZNlVFYUZveFVweUtXRkxybGxvN2hKa3lObXdkNnByU1hTRmlDdTFNRUE1QnVWeWtLZzQrQkt3bU1NQUQ5Vk5VbXBTdnpBNC9wWXhlTlE5WDIxWlZZRkszNFc2T1NKWVZUVGtBZ25jQ3gwR0IzTWZkKzArazNVdW55MC9ZcGFrWlFHZXlTWlNsNG45TVdSOUg2YjF4ZVRUWll6dlhid1dONHdnZjdEZEdPalZYUXZlaFZ4Ukg5RDR6OUpsdFNqV0ZOWDdHTnB6a09LZXN0VndTWUxlOGk4N3hRc1AzYUI5VStwWG5DWk9nd1EySVJKdHZlWmtUdE9uVlg5bFFtb2ZiL09jenFsUG90MDk5K2tZR0M0cXE0aU9MOFU1M2ZaNWY4U1ZNUzRMNkdYVFN0UDhhaDZNNExpVXVITmVvdHpIUExVeTVab3drUDJDV0ZrcW1LVFV1YnZMdjNyS0NZam4rQkhyaW9nNU1MSG9ZazA0TWxZcTRoVXY5TWlkdHBzYUpCSjQyOEZyZDZxUjEyS0pES1d0Q3Q1cVF4ZkI1U0diMTdGckIwdWZyeXUiLCJtYWMiOiI5MWRhY2U3OTE2ODllYTM1OTdjZDA5NDQwOWJmNTg0ZjMyMWJkNzdiYmUyYjJhNDA2ODcwMzdmZWUzNTNiZjUzIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:58:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkEzSkpHWjNLazE1SEp3ZkdnblZXSXc9PSIsInZhbHVlIjoidTZLYXZFQW9nQUFzQmVJOHFVdHIrU21pOXJweENYNTdrbzF1azc2UlJQZHR5ekpkbGJ3bVgyL05wOFNjVGRTQ24wSkhjTmdqUmx3V1FzK3ZQQkJPcTMvVTJ5TUQyR1BjVFBxTkhtYmljRHRpWHJGVWhQemZpbEN3Wlc1V3paeXNhdlVEVXo0NDkwNHZXRUhuQ3dvNlFVVFdyNmw5R29ZVGFvcmJMWDNxWUo2WmNqWGhSekNlelh2MG9xZUZJYnF0bDIweWgxbmNQdGMrQm5xcGR5YUlmZWZBbGJsTGpQaFBpQ3RmdmFVRWhVM09JdWgrZEZ5OFM5OTF1NWFDM2RIZHpWbTVFRXVkVm5wcU04R0xONzNKUUJTMTNCWTA3OVNtOEJ4cnI1am5RK2pTOHZFY0ZiZ3hPOU4rMFpQdlpJU2RWTGpORXlobU9Bb0Y5NWpWWWR5dnhqNTBXd3NUY3h4QzlmS3NkSCtMZUw2L2VPQnRsZHpEVmpaVW1rSDI1ekUybG5oR2FjUVhmN0kyVUZnelhQRnZmZnJ2Zno4cDZGZjRSNmdueWxIUzFoQVBxSnBmSHBRK3hQVURpTnJZVUVORmV5cmh1eEVRWGNMNXZQTjF4SkRiR1Jpdk8wRm1UYWJiR0JBMHdwUFZ5OWlFKzhpb0hwcU5yYjVmUzZvTE9TWloiLCJtYWMiOiJiNGJjY2VjYzhjZDQ4ZGI4ZTYxNDliZDE4YmUxOWQ4YTllNjk5NTliYzhlNDY5Y2RjOWQxMWI4MjVjNThhMzEzIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:58:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNDWklHWDVXZ2QveXRTSGFBT3Avamc9PSIsInZhbHVlIjoiMVpZNW9mR1ZldEFNZE9zKzB0STdRdHN4dFVJMDU1S3RQRDVTeVNJRjFUNEZyb3NmZGxhQ1VFU0RldXZDZGtyblo5Y3ZqYXFPbWdDK2xGdlkwOEtmRFBZNlVFYUZveFVweUtXRkxybGxvN2hKa3lObXdkNnByU1hTRmlDdTFNRUE1QnVWeWtLZzQrQkt3bU1NQUQ5Vk5VbXBTdnpBNC9wWXhlTlE5WDIxWlZZRkszNFc2T1NKWVZUVGtBZ25jQ3gwR0IzTWZkKzArazNVdW55MC9ZcGFrWlFHZXlTWlNsNG45TVdSOUg2YjF4ZVRUWll6dlhid1dONHdnZjdEZEdPalZYUXZlaFZ4Ukg5RDR6OUpsdFNqV0ZOWDdHTnB6a09LZXN0VndTWUxlOGk4N3hRc1AzYUI5VStwWG5DWk9nd1EySVJKdHZlWmtUdE9uVlg5bFFtb2ZiL09jenFsUG90MDk5K2tZR0M0cXE0aU9MOFU1M2ZaNWY4U1ZNUzRMNkdYVFN0UDhhaDZNNExpVXVITmVvdHpIUExVeTVab3drUDJDV0ZrcW1LVFV1YnZMdjNyS0NZam4rQkhyaW9nNU1MSG9ZazA0TWxZcTRoVXY5TWlkdHBzYUpCSjQyOEZyZDZxUjEyS0pES1d0Q3Q1cVF4ZkI1U0diMTdGckIwdWZyeXUiLCJtYWMiOiI5MWRhY2U3OTE2ODllYTM1OTdjZDA5NDQwOWJmNTg0ZjMyMWJkNzdiYmUyYjJhNDA2ODcwMzdmZWUzNTNiZjUzIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:58:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkEzSkpHWjNLazE1SEp3ZkdnblZXSXc9PSIsInZhbHVlIjoidTZLYXZFQW9nQUFzQmVJOHFVdHIrU21pOXJweENYNTdrbzF1azc2UlJQZHR5ekpkbGJ3bVgyL05wOFNjVGRTQ24wSkhjTmdqUmx3V1FzK3ZQQkJPcTMvVTJ5TUQyR1BjVFBxTkhtYmljRHRpWHJGVWhQemZpbEN3Wlc1V3paeXNhdlVEVXo0NDkwNHZXRUhuQ3dvNlFVVFdyNmw5R29ZVGFvcmJMWDNxWUo2WmNqWGhSekNlelh2MG9xZUZJYnF0bDIweWgxbmNQdGMrQm5xcGR5YUlmZWZBbGJsTGpQaFBpQ3RmdmFVRWhVM09JdWgrZEZ5OFM5OTF1NWFDM2RIZHpWbTVFRXVkVm5wcU04R0xONzNKUUJTMTNCWTA3OVNtOEJ4cnI1am5RK2pTOHZFY0ZiZ3hPOU4rMFpQdlpJU2RWTGpORXlobU9Bb0Y5NWpWWWR5dnhqNTBXd3NUY3h4QzlmS3NkSCtMZUw2L2VPQnRsZHpEVmpaVW1rSDI1ekUybG5oR2FjUVhmN0kyVUZnelhQRnZmZnJ2Zno4cDZGZjRSNmdueWxIUzFoQVBxSnBmSHBRK3hQVURpTnJZVUVORmV5cmh1eEVRWGNMNXZQTjF4SkRiR1Jpdk8wRm1UYWJiR0JBMHdwUFZ5OWlFKzhpb0hwcU5yYjVmUzZvTE9TWloiLCJtYWMiOiJiNGJjY2VjYzhjZDQ4ZGI4ZTYxNDliZDE4YmUxOWQ4YTllNjk5NTliYzhlNDY5Y2RjOWQxMWI4MjVjNThhMzEzIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:58:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-234496633\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1864883049 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Ua5BrbNDPybjMH5VC8eOstHjXjHoF5PPn8eQugo0</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864883049\", {\"maxDepth\":0})</script>\n"}}