{"__meta": {"id": "X96286e3f4b81bc70c6be073d22840988", "datetime": "2025-07-29 05:58:07", "utime": **********.571843, "method": "GET", "uri": "/api/leads/19", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753768686.704139, "end": **********.571863, "duration": 0.8677239418029785, "duration_str": "868ms", "measures": [{"label": "Booting", "start": 1753768686.704139, "relative_start": 0, "end": **********.454962, "relative_end": **********.454962, "duration": 0.7508230209350586, "duration_str": "751ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.454998, "relative_start": 0.7508590221405029, "end": **********.571865, "relative_end": 2.1457672119140625e-06, "duration": 0.1168670654296875, "duration_str": "117ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46064576, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/leads/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@getLeadForPreview", "namespace": null, "prefix": "", "where": [], "as": "api.leads.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=440\" onclick=\"\">app/Http/Controllers/ContactController.php:440-476</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.011590000000000001, "accumulated_duration_str": "11.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.511392, "duration": 0.00841, "duration_str": "8.41ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 72.563}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.534871, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 72.563, "width_percent": 8.369}, {"sql": "select * from `leads` where `id` = '19' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["19", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 446}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.539999, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:446", "source": "app/Http/Controllers/ContactController.php:446", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=446", "ajax": false, "filename": "ContactController.php", "line": "446"}, "connection": "omx_sass_systam_db", "start_percent": 80.932, "width_percent": 7.506}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` in (118)", "type": "query", "params": [], "bindings": ["118"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 446}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5496361, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:446", "source": "app/Http/Controllers/ContactController.php:446", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=446", "ajax": false, "filename": "ContactController.php", "line": "446"}, "connection": "omx_sass_systam_db", "start_percent": 88.438, "width_percent": 6.903}, {"sql": "select * from `pipelines` where `pipelines`.`id` in (30)", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 446}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.554262, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:446", "source": "app/Http/Controllers/ContactController.php:446", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=446", "ajax": false, "filename": "ContactController.php", "line": "446"}, "connection": "omx_sass_systam_db", "start_percent": 95.341, "width_percent": 4.659}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/api/leads/19", "status_code": "<pre class=sf-dump id=sf-dump-342567274 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-342567274\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1033084864 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1033084864\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-497458363 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-497458363\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Im00UTBBaEJwT0JvYkRaNlkzeFJlaHc9PSIsInZhbHVlIjoiTE44SlJIb0hFbi9zUG5PY3lGa1JsTjdIaVJTMXNrejkrb0piWXlyL0JMV1huOWczT3ZVekVXZWNTbTE5UkFoaHlYSTFwZGdmLzU1bUkxR0p5T3M0Zm9KYlJ3VGJ1L3ZGQktESGZTZWM0azlBZ2p3U0o4RkhLT1MxYk1IYi9UR0lCSU5aUnF1eXlyb21UZFNORFpDb2ZJSkMwMzZsMlU3NzJBZmxnVVVTWlF6bndRczZNckpLc2lhbG1TYmlzSTRUSFF6TGI0NzZ1b29KODJwYWtJaWpnS1M1aWxlRW5vdmhPYWxkWW1HbmozMGFSNDdWU2R5YTI0SHZQTEo0ZE9HUFh2TlY4WWplOXU3VTROZEZoWnJURm5sWWpRYzFkM2VOZVduWGI3aWF5NGJYVGlOSGlyeUtQdVpzWVgvSyt1elpOQjg1QzRuNEFRVUtxWGo2dlFGUDZZaHBHUFVlRlo0a3JEUDRHTnpJSFdNanBQV2hVdmVYQnowTzBUelJEdzhUQUswSWZqTUdOQVRVb2RUK1p6QTVSd3hteGFMME1ia3pJbVlvRXhwOXVHWHgycHVqZkhQZHdSNkM2Yk15SUo5NElWVFM1Q0VONnpOMk5YbEp3R0FlQ2JIK3dFYUxicU9OTFFqcUJNR2FHZGdoRVdhbjdKcEJhNUx0MnUzK3NudzUiLCJtYWMiOiIyMmNiNWNiNjEyYWQ3N2VhYmVjY2M4ZjRhYWMwNjVmMTM1YTNmNTE4NmQ1MGQ5MTRiMmZmNGQwYmM1NjZiNWU0IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InlqK280OEtkb0V5ZHBxTVh4U1BUTHc9PSIsInZhbHVlIjoiTGlBRmN3ZVE2MHo0OWpiMHMvQkErZUt4K2MzY1NBb0NRdlBhZkhmSkFOVGdSVTV4Wi9sWWZsN0t0S0tPSEZPUFdZVU1FVUhyNmtDaTZSeTdBQ3VOdDUwdWQ2TTMzb1dYd3BVMlg1NjFIbzFUbytZL1V1ZWY4WkxjcmdGbnRkNDI2TUlTSDNtRUlpU2V4RWcwMUs5aWpIc2VqdjdVQ2lDOXVYN243eWtIaVpoeHY0NzR4eGNJZFJEQVhQMkkxK2FFQ09Ta0Nlenp2UkZYcXA2a0pxWTM2QitKSUViK2hPUlNqNWRPd3hKTkRuWUZ3UjRBbG5OenRFc0VuOUVLQldXTmxPRktBdjBZRzBJV3MvdUp5OGZLVFBxcVJpV1JVTVRNR3hDRHRnbWJwbjhuUjBXTjZwL29YTWdoSm51bWdxTGFnWlA1d2FrNnRIT05iUDlNV2VnK3BDTFBzdFprVVpoc1poRENXVnd3dUtaR2x4c01ndy95YlRzMUp5SkdzZDhNWHBvUExES3VzSjF5dDBSa1FDMmZxS09HMUNVcmVUUms5anZjL2Z2VzM5OFN0MUNIUVBNTlc0ejNEdG1IcnFRWGlRMDJEWVlTaXR3Y1pUSW5qUmJZamJwNUxrQ2ZLUk9rLzlYZG1UMUpXdkJNUmVzeTZ1TER1ZHljeGRJQzVPMysiLCJtYWMiOiJjNmUzZmZkZTNjOGI0ZTkwNTI2MDVlNmJlNTkwNmQwYTFlMjczYjI3YTkzYTAxYTU2OTdjMmFkNmIyNjQ2N2Q1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2090126395 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2090126395\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1437505169 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:58:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtldW9WV3hLYVZRZE5VbEZBby9vTFE9PSIsInZhbHVlIjoiTWVSTklZcWVvZjJJb1Z5elVrK1VzbHJiTjRqamwxMnFKMVI1WWhiT3JpZk01eGc4S05BWWladEFoSVJEUXhxWDdkaWoyU2tkRSt5RTN4VlBtdUV0UGJrSUh5dW9hc1pWREpiNFZXTHY5UmpLdXZiWEZvVDhydUJzZ0lIS3dMd0FQMDJFMS95SVg0c1lzcmNEQVVNbmJkUEhCZW56ODhiYkZ3L0ROUldGSC80RzIxV25TN2YzdExJNGh1YzN1VU1vQnMvTWEwVzVnUDAzenVCN05BVEZoL2N6N0xOdkZkUnhIVmtIUjZNTGg5cy9KTmwzd0h0aGVGV3VlVDZMRGhUSDUwdG5yaHdycDN1UlhJYm5pSzRCTjkzR1JSTFNTQ0tSVlV5OGdoQTJPWEw1RWYwSDJyNWF4TUZCbmtBTzFxQkU3MTdoOGIydmhWQittcTFaYXRpS2VNSmJQUWZwMVIyV0xCbTMyeWNNbkNoM09LUnIrbWhUZnRESFcyR0djclRTTmtORnRPdW9hQVBmbkt6WlpGaTBMajdnZzRoTVU4WTluWENwMzN1azFHNExjVzFET3Y3eUQ0UkpYaldjeFkxdzJ1QlY1ZDYxd1RQOW1iVnY5ZEd5Q2JKQ1hoVkZkZ01XMnlxdG03dXExNmJ4a1QrTkF0Q2x1emFGUndYYlcxTC8iLCJtYWMiOiJhZjdhZGUyZDk3ODg3YmVjMmQ5YWIwYzc2ZGY0ZDA2ZWZkNmYzMTJhOTBmZjdmMzc4ZjczMzg5Y2RmNTdlYmJmIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:58:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjlnOWtGSm1HazFHOFFrenJYeXN3QXc9PSIsInZhbHVlIjoiL3RxOWh5SmhNUXZBRVRpRjhST09hZXl5TG9jcWlmRndDMmxGMFByYVVJaSs5OFE4U2hGNlljS2xQOEhEM3JsRGltZG5CTVUvV2Q4RUVIWVI4WGI4VStRcStPRkxraVNtNzRvSU9OTEFKdVQ1MnlMMGhQZ25uOXZrM1I2VTdISkVOSnVnQlVKZjNnSWR2emtiK1VyRmhrQTd3N2QrQ2pHdEtDWlpqRFdEQVBXaHd2RCs1eDBrZCtMWlVSOG5aMFJxUm1kenNDaHNQbFh2N2xjUm5Kbk5CemZhcCtIVmJCR0JZVGhqcWh3M1U1ZWRVSVJxU1FPMkJEbU8zL2dDZ0pZTXRHallwanZDVFZjMUxLdzczZ0dLYjFpNEI0T2hLOGpaRk1DVzhPaXptM2VqbW1XMEpvaWJFWVVEclVqOVFwVmVyV1NzUFpqNHdheWs4VUY4SFdacTNYcXJoelFyUmtTNlNrTW9LdFI4L2lRK0UxWFFNOXdDaXlTU1RrY3ZaWXpPU0U4MzFTdXlRekM3am1DTEF6TDh3eWZXZTR0a3hGK3EwMXphWnhBMytHbXExNGIzRnczZUNCTDlaN0tsMHpxa2cvcW1LVXY4OWdVUlV5WFlvQTJwdmRzRWtNTGU3aWZ0d0pzRGtkWHgvN1JHaDlubGdRYVJna2NncGI2QVVOb3EiLCJtYWMiOiIxMzU1MGNlYWRlZWQwNWU3Y2QwMTk1MGUxNDg0NWZkZGY5ZjA2ZDAwOWI4NDA1OTdmOGExZWZmODZkNGZkNWZkIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:58:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtldW9WV3hLYVZRZE5VbEZBby9vTFE9PSIsInZhbHVlIjoiTWVSTklZcWVvZjJJb1Z5elVrK1VzbHJiTjRqamwxMnFKMVI1WWhiT3JpZk01eGc4S05BWWladEFoSVJEUXhxWDdkaWoyU2tkRSt5RTN4VlBtdUV0UGJrSUh5dW9hc1pWREpiNFZXTHY5UmpLdXZiWEZvVDhydUJzZ0lIS3dMd0FQMDJFMS95SVg0c1lzcmNEQVVNbmJkUEhCZW56ODhiYkZ3L0ROUldGSC80RzIxV25TN2YzdExJNGh1YzN1VU1vQnMvTWEwVzVnUDAzenVCN05BVEZoL2N6N0xOdkZkUnhIVmtIUjZNTGg5cy9KTmwzd0h0aGVGV3VlVDZMRGhUSDUwdG5yaHdycDN1UlhJYm5pSzRCTjkzR1JSTFNTQ0tSVlV5OGdoQTJPWEw1RWYwSDJyNWF4TUZCbmtBTzFxQkU3MTdoOGIydmhWQittcTFaYXRpS2VNSmJQUWZwMVIyV0xCbTMyeWNNbkNoM09LUnIrbWhUZnRESFcyR0djclRTTmtORnRPdW9hQVBmbkt6WlpGaTBMajdnZzRoTVU4WTluWENwMzN1azFHNExjVzFET3Y3eUQ0UkpYaldjeFkxdzJ1QlY1ZDYxd1RQOW1iVnY5ZEd5Q2JKQ1hoVkZkZ01XMnlxdG03dXExNmJ4a1QrTkF0Q2x1emFGUndYYlcxTC8iLCJtYWMiOiJhZjdhZGUyZDk3ODg3YmVjMmQ5YWIwYzc2ZGY0ZDA2ZWZkNmYzMTJhOTBmZjdmMzc4ZjczMzg5Y2RmNTdlYmJmIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:58:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjlnOWtGSm1HazFHOFFrenJYeXN3QXc9PSIsInZhbHVlIjoiL3RxOWh5SmhNUXZBRVRpRjhST09hZXl5TG9jcWlmRndDMmxGMFByYVVJaSs5OFE4U2hGNlljS2xQOEhEM3JsRGltZG5CTVUvV2Q4RUVIWVI4WGI4VStRcStPRkxraVNtNzRvSU9OTEFKdVQ1MnlMMGhQZ25uOXZrM1I2VTdISkVOSnVnQlVKZjNnSWR2emtiK1VyRmhrQTd3N2QrQ2pHdEtDWlpqRFdEQVBXaHd2RCs1eDBrZCtMWlVSOG5aMFJxUm1kenNDaHNQbFh2N2xjUm5Kbk5CemZhcCtIVmJCR0JZVGhqcWh3M1U1ZWRVSVJxU1FPMkJEbU8zL2dDZ0pZTXRHallwanZDVFZjMUxLdzczZ0dLYjFpNEI0T2hLOGpaRk1DVzhPaXptM2VqbW1XMEpvaWJFWVVEclVqOVFwVmVyV1NzUFpqNHdheWs4VUY4SFdacTNYcXJoelFyUmtTNlNrTW9LdFI4L2lRK0UxWFFNOXdDaXlTU1RrY3ZaWXpPU0U4MzFTdXlRekM3am1DTEF6TDh3eWZXZTR0a3hGK3EwMXphWnhBMytHbXExNGIzRnczZUNCTDlaN0tsMHpxa2cvcW1LVXY4OWdVUlV5WFlvQTJwdmRzRWtNTGU3aWZ0d0pzRGtkWHgvN1JHaDlubGdRYVJna2NncGI2QVVOb3EiLCJtYWMiOiIxMzU1MGNlYWRlZWQwNWU3Y2QwMTk1MGUxNDg0NWZkZGY5ZjA2ZDAwOWI4NDA1OTdmOGExZWZmODZkNGZkNWZkIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:58:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1437505169\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1044743257 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1044743257\", {\"maxDepth\":0})</script>\n"}}