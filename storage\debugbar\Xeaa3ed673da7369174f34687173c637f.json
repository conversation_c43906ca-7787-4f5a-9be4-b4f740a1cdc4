{"__meta": {"id": "Xeaa3ed673da7369174f34687173c637f", "datetime": "2025-07-29 05:55:50", "utime": **********.122678, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753768549.201668, "end": **********.122702, "duration": 0.9210338592529297, "duration_str": "921ms", "measures": [{"label": "Booting", "start": 1753768549.201668, "relative_start": 0, "end": **********.036349, "relative_end": **********.036349, "duration": 0.8346810340881348, "duration_str": "835ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.036376, "relative_start": 0.****************, "end": **********.122712, "relative_end": 1.0013580322265625e-05, "duration": 0.*****************, "duration_str": "86.34ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PH2LA5wMUVouH5yuzJ3VENiV0Zs7KGjRWG3FEdZ7", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-584192055 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-584192055\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-377872925 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-377872925\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1933431662 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1933431662\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1745006320 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1745006320\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2097784171 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2097784171\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-99766130 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:55:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBEWm9ZMWtYQ2ZlMGYxelhLTGJ2RHc9PSIsInZhbHVlIjoiSk5FWjhBQVJrendDa2t2cWdYclpsNzVNZXc1bFlkdDhUTnVLaVRYOUFwRW5aT1pvOXVkWnd2VUFPdjVieXpIeWtiSzBzbmtFV2h4cVRORTc5RC9aaWR1d0lEQVl4WmR0V0YrQytwWnZLRFJkQUYwTG94aTJ2NmhkdFlpTE9vWlNlbWwxeTNzZTlHbXVtS2ZEU1c0M1JTa3hpSEp4djh0TGpNK1h0dUQwa2pYOFU3SFRTRWlMSnJhQk1iNFprYUdibTExRkFnc05Xd0Z1cUVmVWFwY1RSS0ZndnltUW1sVVpqbUt2bG53Vlo4bkIwL1hSMitnaHdQWlVGcVkvd09aNGlKMXJSNzhVYVhsNTlWYmQzMXMwUmsrQVJZbi9SUjJRb1pnUmFqSzliejFZYlFCVzdQdFl6OVBLYStSUHBJdUxJbE16RStxU3ZlQ2E2R0pFemp4OGpLNEhneS90aURpcUhwdTRKb2xJN1ZmR2pheFM3S1Q0YzhmMmVqVng0MUM5MlBFNE5hTUNOMFNDSk50a3ZQRjU4V3pwQVFvWTBvVUdaeVBYN3d2SDNMTEtKcFNCOEQ3Tm1wb0ZySnp4U0NoL2NPSm5vUmp0eGtPQjVVL29RZzBQOUxmUXU3UHNjd3VJMEo3bWkyZE5pOFpnN21jd0pBdXc0eWNkNnk3Wjlrck4iLCJtYWMiOiIzMTZhYzgxZjgxZjQ5ZWQxYjViYTllYjQ2OGIxMDE4MWFjODY4YmRkOWIyNTE5Zjk3MThiNjc0YmRhMzkwOWI0IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:55:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImFtcWpTVXc3VFV0NUpqa2ZPdnBzUFE9PSIsInZhbHVlIjoiOTBjdmQ5MUt0bnlocjR3ZDJORElMeXorSnlhZ2hXc0lYakVqQkdyb0JiYzQzc05hOFYrVkMxOXc1WnYrK2M0bWwxUk1VWjVoM202aU9HbHZqaUVGc0V3SCtKaE9nYXRrRS8zeXpuVWdkTXpNSVJlSFh3dG1DV1dYSHZKYnh1TFhuTnJTWno3cisvaDRQeldnTUp6WWZaOC9RQi9jcXFDODA1elJob0lJWi82bGM0K1VlVUhvMXlpYTJ5MkFRanJ2RUlOOW1idWJTanpGMWh6RXgzWWw4TnU5VTNWMUdiNjduMzJaYTV1d0ZIVmJ0Z3RjeDNxdFBKWGpTYWM2NkJqd0J4Y0g4SnphWWdKdTBBVWJ1RWw4NUYydlVCb0JJQ25EbVdzKzNqVHgzTlMxWHE3ZllaQlU2Y3J5RnAyRWNxOEppQ1FabUNJeTBsNDNYdmJXWGhuMk82cURIVHh2ZlFZUXlOR1g3WFZMT3hLSC90bkwyNmhHTUJmVHhtNWpScUxTRUx2QkpZSzYwRWVuSHNPc0pFd21aWjd1QXFHdlMrL3pLOGk5MklDOVd2WllWMy9wTEFNbEthQWs4SCszZG0xQW5iK0tWY2JqRXAxa2pIU0VlVWdKMHpkK2pFR2YvRWYrOWNFL0VBQyszM0g0b045VktxWnF2anFHczZkbEE0VzIiLCJtYWMiOiI5MWE4NDEwMzRlNmYxNDZkYWE2NjFlY2IyOWE2ZWNhNGFlMzUzYmE3MzhmYzU4YTJmMGJjMjY0MDg4OTMyNjg0IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:55:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBEWm9ZMWtYQ2ZlMGYxelhLTGJ2RHc9PSIsInZhbHVlIjoiSk5FWjhBQVJrendDa2t2cWdYclpsNzVNZXc1bFlkdDhUTnVLaVRYOUFwRW5aT1pvOXVkWnd2VUFPdjVieXpIeWtiSzBzbmtFV2h4cVRORTc5RC9aaWR1d0lEQVl4WmR0V0YrQytwWnZLRFJkQUYwTG94aTJ2NmhkdFlpTE9vWlNlbWwxeTNzZTlHbXVtS2ZEU1c0M1JTa3hpSEp4djh0TGpNK1h0dUQwa2pYOFU3SFRTRWlMSnJhQk1iNFprYUdibTExRkFnc05Xd0Z1cUVmVWFwY1RSS0ZndnltUW1sVVpqbUt2bG53Vlo4bkIwL1hSMitnaHdQWlVGcVkvd09aNGlKMXJSNzhVYVhsNTlWYmQzMXMwUmsrQVJZbi9SUjJRb1pnUmFqSzliejFZYlFCVzdQdFl6OVBLYStSUHBJdUxJbE16RStxU3ZlQ2E2R0pFemp4OGpLNEhneS90aURpcUhwdTRKb2xJN1ZmR2pheFM3S1Q0YzhmMmVqVng0MUM5MlBFNE5hTUNOMFNDSk50a3ZQRjU4V3pwQVFvWTBvVUdaeVBYN3d2SDNMTEtKcFNCOEQ3Tm1wb0ZySnp4U0NoL2NPSm5vUmp0eGtPQjVVL29RZzBQOUxmUXU3UHNjd3VJMEo3bWkyZE5pOFpnN21jd0pBdXc0eWNkNnk3Wjlrck4iLCJtYWMiOiIzMTZhYzgxZjgxZjQ5ZWQxYjViYTllYjQ2OGIxMDE4MWFjODY4YmRkOWIyNTE5Zjk3MThiNjc0YmRhMzkwOWI0IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:55:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImFtcWpTVXc3VFV0NUpqa2ZPdnBzUFE9PSIsInZhbHVlIjoiOTBjdmQ5MUt0bnlocjR3ZDJORElMeXorSnlhZ2hXc0lYakVqQkdyb0JiYzQzc05hOFYrVkMxOXc1WnYrK2M0bWwxUk1VWjVoM202aU9HbHZqaUVGc0V3SCtKaE9nYXRrRS8zeXpuVWdkTXpNSVJlSFh3dG1DV1dYSHZKYnh1TFhuTnJTWno3cisvaDRQeldnTUp6WWZaOC9RQi9jcXFDODA1elJob0lJWi82bGM0K1VlVUhvMXlpYTJ5MkFRanJ2RUlOOW1idWJTanpGMWh6RXgzWWw4TnU5VTNWMUdiNjduMzJaYTV1d0ZIVmJ0Z3RjeDNxdFBKWGpTYWM2NkJqd0J4Y0g4SnphWWdKdTBBVWJ1RWw4NUYydlVCb0JJQ25EbVdzKzNqVHgzTlMxWHE3ZllaQlU2Y3J5RnAyRWNxOEppQ1FabUNJeTBsNDNYdmJXWGhuMk82cURIVHh2ZlFZUXlOR1g3WFZMT3hLSC90bkwyNmhHTUJmVHhtNWpScUxTRUx2QkpZSzYwRWVuSHNPc0pFd21aWjd1QXFHdlMrL3pLOGk5MklDOVd2WllWMy9wTEFNbEthQWs4SCszZG0xQW5iK0tWY2JqRXAxa2pIU0VlVWdKMHpkK2pFR2YvRWYrOWNFL0VBQyszM0g0b045VktxWnF2anFHczZkbEE0VzIiLCJtYWMiOiI5MWE4NDEwMzRlNmYxNDZkYWE2NjFlY2IyOWE2ZWNhNGFlMzUzYmE3MzhmYzU4YTJmMGJjMjY0MDg4OTMyNjg0IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:55:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-99766130\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PH2LA5wMUVouH5yuzJ3VENiV0Zs7KGjRWG3FEdZ7</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}