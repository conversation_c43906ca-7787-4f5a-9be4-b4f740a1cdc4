{"__meta": {"id": "X52b92e8dcf9600f97200808a66d09073", "datetime": "2025-07-29 06:12:54", "utime": **********.624392, "method": "GET", "uri": "/api/leads/20", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753769573.459984, "end": **********.624427, "duration": 1.164443016052246, "duration_str": "1.16s", "measures": [{"label": "Booting", "start": 1753769573.459984, "relative_start": 0, "end": **********.509305, "relative_end": **********.509305, "duration": 1.049320936203003, "duration_str": "1.05s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.509324, "relative_start": 1.049340009689331, "end": **********.62443, "relative_end": 2.86102294921875e-06, "duration": 0.11510586738586426, "duration_str": "115ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46071824, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/leads/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@getLeadForPreview", "namespace": null, "prefix": "", "where": [], "as": "api.leads.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=478\" onclick=\"\">app/Http/Controllers/ContactController.php:478-514</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.0074199999999999995, "accumulated_duration_str": "7.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.567127, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 54.717}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.584461, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 54.717, "width_percent": 14.69}, {"sql": "select * from `leads` where `id` = '20' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["20", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 484}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.591554, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:484", "source": "app/Http/Controllers/ContactController.php:484", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=484", "ajax": false, "filename": "ContactController.php", "line": "484"}, "connection": "omx_sass_systam_db", "start_percent": 69.407, "width_percent": 9.704}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` in (118)", "type": "query", "params": [], "bindings": ["118"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 484}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.599767, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:484", "source": "app/Http/Controllers/ContactController.php:484", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=484", "ajax": false, "filename": "ContactController.php", "line": "484"}, "connection": "omx_sass_systam_db", "start_percent": 79.111, "width_percent": 11.186}, {"sql": "select * from `pipelines` where `pipelines`.`id` in (30)", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 484}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.605959, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:484", "source": "app/Http/Controllers/ContactController.php:484", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=484", "ajax": false, "filename": "ContactController.php", "line": "484"}, "connection": "omx_sass_systam_db", "start_percent": 90.296, "width_percent": 9.704}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/api/leads/20", "status_code": "<pre class=sf-dump id=sf-dump-1796650830 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1796650830\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-638369849 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-638369849\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1967388287 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1967388287\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1794279873 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IndmaVhWdk5OU0RicjlteWg0NklBN1E9PSIsInZhbHVlIjoiWDBDT2hmcG1SZTloQ1ZRZnh3NWxETEFKWUNpeVkrUFZqczZCcDlUMElHYTVzYWwza2NFVXhsRXR3d2VGS2RGZGJ2anM4ODM4aVI0VThnWDBHdkhONWRzWU42eUxwc2FVeHZlbFdadXRVTFh4V3laYTVRR0syY0M3UmJtUU1ndTEwc0poTUxtVmZTK2d3WXdnSHRLaGRiTnFWc1grNUhNZmgzR2xkVVlzRWRmV0tGYVZ5clB6ZG1ZLzF6b1Irb1daYk1maXV5ZjRQaUc5L2dDakNpVXA4akpJTEwvQk5HTUptR2NESkJmT284NU91T3V0NGIxWndOL0p4VXpKbjBleDZwZkF0clNqak5Gd24yNFNyK0t4YUx2QVFTZmRsWVR6bGFXaklnMkdYaWgvTTlpNE92TGJCemJiQXA4WUFQOVhSSnZRWldMOFhUS0RWNHdEZS82OXlpUGhFZFRqZHQ0OVNPbWJ6RkpJTTFQY0ZnV3lNWUNzVWpXZjJJR0xPMGxPanRQcUovTnRwN1F6dFl5MXlNdnJGY3dmU2lKNlJnUjAyS3oyaHp3c0VTQXNFbkE3WTExK0xaY1pYakN6ZWRNaWpCeHU2UkRZVm1vR2x0UVd4M3g5dWgyUU05RHhqcE85OFVJTEpEYmx0bDBvTFNxNk4vUTJtaXFpVy8vOUgrd0wiLCJtYWMiOiI4ZjQyYTc0Y2I2MWU0NzY3ZTQzODY0MjA1N2EyY2Q2Y2IxMzA1YWM5YThmOWNiYjFlZjU0MzkyMGY2NTU4YWMwIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlFsb0xxMVJCVkFKZzJWN0RvYWlsOUE9PSIsInZhbHVlIjoibjZUekw1bEJ3NWM4TFYybzFqck9ES003WXZxK1pKY2RnbTR2TXhwMGFzY2xrU29nM1Z3anlJcGNvbGRiMzErYisrcUtsMzBUWGFpRTRqaDhGc2MyS0hSejRkMDFEVEJwdW5rU2lMU0FHWWZiSFFEM0FjbFVJVUhZQnhKQmdmN1h1cWNOVVkvQ0kxR0hwUWVpa1Q3NHlUUnA1emlpTmRLNEZqRGRyY0RaajBnVyt6bXh0RXpnaS8vRVlGKzFuL0FVUzljZXQ4QjVhTTVRM2dlejBQRGF5REpadXVJQXAyb1RpaHFJQmN4ODczQVlSOE9hVXVBbkpTVnRYUTBmejQrdWZRSjFhRXRnUzVOM2kzdEhiZ250MWl0VlR6dGpmaktManVaK1Y3dkxGWllhZ3RjSU05VWE3eWhlL3crRXoxbDdmMXZPRnBkT0wrM1JhV0hHOC9yNVNNbE10a0VvS05Va2t3QlJVSTd3a0o1Z3lXT0hTY0ZCVjRhUllTbUM3blVpcjQ2K2MwaDhxalJ3WXBHbEVwc1RsNVYzb251MXVwMHRmbGdXVzkwVnJRbXNrQjdHMm5HZE9OdFJ6OGt6SFlUN1pOL2dDWGpzRVMvaWhCRFdYNmtnUEZWSWFqa2h4TUJZUFdYVS9OalBNeFhhY1ZjMStGMEdweVM5VEhySGpKcGUiLCJtYWMiOiIzZjQ1ZjZjOTk1NzdmYTVhODcwOTA3YmZjMjJjYjM1ZjJkZGM5MDQ2MWNjMDI0NzA4MmVlMjZkMWE1ODIzMGM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1794279873\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1264201973 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1264201973\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2062071472 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:12:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9ndkcwQ0FtU2hpem9hUjZGRzFZY2c9PSIsInZhbHVlIjoiVXFZV09jYUkwSSsrODJ2elJnMVhoM0FsUktUSkpya05ZWVMxaXovSjBsVHpZMjBOaWx0a2I3L3lKNlo5bU5ueUNaOUFEQ2JzVnhBbmJSaDZabkdlRjRJRTVONll2ZXhONlFoUlJQakk0NFJlOURaYjBWaFNLRHdxcGw0UWRIMm1vT0lRNE5LZkdsWnIzOXUzci9rSWEwcjNGb3RwaDhuOU9PUkp0cjFnMDA3dUptYzRpZ0xYR0pwVHVpTFF6UVYxaEF2RW5ialcyeHNIV0dBS1BjSjZzTWxWaU5QcllnK1MrU3RjRkJyZ29nb2p6YURpUytJcDZnKzNXOHZjejRCTjVVeDJ1VllsNFhrdnltd2NTSDcvOTZTS2VLMGZzeEpmS2NhTm1Gb2JXY24rWFJ0czVJb1ZTY2o0cHY0OXRmMHA0WDdDRG5nb3RwL0h1SmI2U3FLVmxTbE1aRFFKMGJ3Q1BCTU5aVUdwZGhLOXlocnNHcHJwelVLRGJnYm1sVkhQMGt3R2FxWUh2Zkh5NkQxR09pT0FLUjZQMjIwWkdxLzFpRlFjZW1abHZORG5KZFJ0c1RwWDhoQTlzSzljaHR5SjVUdVNnQ21Wb2x0cm5vU0FNTmZMUXBvUWIwTHBZRTJqWklRMHpSSDFxN0k4cmN1ejFqb1poSnp2Ym82QmUvRlEiLCJtYWMiOiIzMjgyMzI2MWQ0OWJmODA0ZGE1OTNmMTE1MTRmYzE2YWRmODA3ODBjMTg4YjhhYjAyOGE2MmJkMGY0OGFkODhhIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:12:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImQwZDkvQUlxWkQ5QS9maU5Tc0NGK3c9PSIsInZhbHVlIjoiY1V1VnhZM3k1Y2plRFdJQ1pSTnpoUHVHVm1zamtuYUl1dGdNRXhIYnFNRUdhY2JjNnBKbm8vSko2S2wrOW90YU1iYXM1K05DMFlzdWQxMGV4ODA0ZURZYU1Ccnlhd0h0ODhmMkxvZnpFN0tCUE9kNGw0eC9McUM1c0g2aXNldmU3UGZwN3NXMnlOSFRQRDBNd04vRDh5UWljcGZwQVZ3R29CRjJmb2lLbGkrUzV2ZmZuTXJ3V1FCVGRyZWlsVEkwc2V5OWt2NDhOb2FVMS9hTHpnakRxTlJwR1pDL295a2xsNXRiRWRoaG05U1V1S0lhcW1rRVY5aGJuZUdSS2hTSHlmNkdFKzhvS09UbUI4RGhoMk1WUjRkUHVqS2g1ejNYSVJUTzVUVXlCT3J6ZDJvMWhlNWR3M2VLeWRjd1hGN0VmbGk5d0pEa2xmNmdRVnUyMnFqWGErcm1ZdkNOam9rVm54Wkd4bXVVTUpZNmhZTU5MeGtDaHlkYWJOVmc1VTJBUG4vS3N6SzJSckFVbmVWMEU0SmM4NUVjU2EzTXdQOGF3bkFGay9XT2lUUkxySlRSMFlUOWd6L1pzcCtNclRBU2UrSEFNQThIeWdXWWgyM0R0WWMzUmg3ZEFyY2ttZzJybTN5Ty8rNGpxbDhKdGFRM2I0SlpLTmppU0x1emdGdlYiLCJtYWMiOiIxMjQwODhhYWZjM2NjZGJmNDIzZjVhYmRjOTBlZGQ1NTRhNDQ3YjE0MTA5ZTQ3N2MzZWVlMjFjYzE5YjdjZjc5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:12:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9ndkcwQ0FtU2hpem9hUjZGRzFZY2c9PSIsInZhbHVlIjoiVXFZV09jYUkwSSsrODJ2elJnMVhoM0FsUktUSkpya05ZWVMxaXovSjBsVHpZMjBOaWx0a2I3L3lKNlo5bU5ueUNaOUFEQ2JzVnhBbmJSaDZabkdlRjRJRTVONll2ZXhONlFoUlJQakk0NFJlOURaYjBWaFNLRHdxcGw0UWRIMm1vT0lRNE5LZkdsWnIzOXUzci9rSWEwcjNGb3RwaDhuOU9PUkp0cjFnMDA3dUptYzRpZ0xYR0pwVHVpTFF6UVYxaEF2RW5ialcyeHNIV0dBS1BjSjZzTWxWaU5QcllnK1MrU3RjRkJyZ29nb2p6YURpUytJcDZnKzNXOHZjejRCTjVVeDJ1VllsNFhrdnltd2NTSDcvOTZTS2VLMGZzeEpmS2NhTm1Gb2JXY24rWFJ0czVJb1ZTY2o0cHY0OXRmMHA0WDdDRG5nb3RwL0h1SmI2U3FLVmxTbE1aRFFKMGJ3Q1BCTU5aVUdwZGhLOXlocnNHcHJwelVLRGJnYm1sVkhQMGt3R2FxWUh2Zkh5NkQxR09pT0FLUjZQMjIwWkdxLzFpRlFjZW1abHZORG5KZFJ0c1RwWDhoQTlzSzljaHR5SjVUdVNnQ21Wb2x0cm5vU0FNTmZMUXBvUWIwTHBZRTJqWklRMHpSSDFxN0k4cmN1ejFqb1poSnp2Ym82QmUvRlEiLCJtYWMiOiIzMjgyMzI2MWQ0OWJmODA0ZGE1OTNmMTE1MTRmYzE2YWRmODA3ODBjMTg4YjhhYjAyOGE2MmJkMGY0OGFkODhhIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:12:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImQwZDkvQUlxWkQ5QS9maU5Tc0NGK3c9PSIsInZhbHVlIjoiY1V1VnhZM3k1Y2plRFdJQ1pSTnpoUHVHVm1zamtuYUl1dGdNRXhIYnFNRUdhY2JjNnBKbm8vSko2S2wrOW90YU1iYXM1K05DMFlzdWQxMGV4ODA0ZURZYU1Ccnlhd0h0ODhmMkxvZnpFN0tCUE9kNGw0eC9McUM1c0g2aXNldmU3UGZwN3NXMnlOSFRQRDBNd04vRDh5UWljcGZwQVZ3R29CRjJmb2lLbGkrUzV2ZmZuTXJ3V1FCVGRyZWlsVEkwc2V5OWt2NDhOb2FVMS9hTHpnakRxTlJwR1pDL295a2xsNXRiRWRoaG05U1V1S0lhcW1rRVY5aGJuZUdSS2hTSHlmNkdFKzhvS09UbUI4RGhoMk1WUjRkUHVqS2g1ejNYSVJUTzVUVXlCT3J6ZDJvMWhlNWR3M2VLeWRjd1hGN0VmbGk5d0pEa2xmNmdRVnUyMnFqWGErcm1ZdkNOam9rVm54Wkd4bXVVTUpZNmhZTU5MeGtDaHlkYWJOVmc1VTJBUG4vS3N6SzJSckFVbmVWMEU0SmM4NUVjU2EzTXdQOGF3bkFGay9XT2lUUkxySlRSMFlUOWd6L1pzcCtNclRBU2UrSEFNQThIeWdXWWgyM0R0WWMzUmg3ZEFyY2ttZzJybTN5Ty8rNGpxbDhKdGFRM2I0SlpLTmppU0x1emdGdlYiLCJtYWMiOiIxMjQwODhhYWZjM2NjZGJmNDIzZjVhYmRjOTBlZGQ1NTRhNDQ3YjE0MTA5ZTQ3N2MzZWVlMjFjYzE5YjdjZjc5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:12:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2062071472\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1229972811 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1229972811\", {\"maxDepth\":0})</script>\n"}}