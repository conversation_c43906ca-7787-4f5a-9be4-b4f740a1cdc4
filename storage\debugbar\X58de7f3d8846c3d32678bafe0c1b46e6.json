{"__meta": {"id": "X58de7f3d8846c3d32678bafe0c1b46e6", "datetime": "2025-07-29 06:12:04", "utime": **********.679031, "method": "GET", "uri": "/api/leads/19", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753769523.732461, "end": **********.679074, "duration": 0.946613073348999, "duration_str": "947ms", "measures": [{"label": "Booting", "start": 1753769523.732461, "relative_start": 0, "end": **********.556595, "relative_end": **********.556595, "duration": 0.824134111404419, "duration_str": "824ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.556619, "relative_start": 0.8241579532623291, "end": **********.679077, "relative_end": 2.86102294921875e-06, "duration": 0.12245798110961914, "duration_str": "122ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46071800, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/leads/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@getLeadForPreview", "namespace": null, "prefix": "", "where": [], "as": "api.leads.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=478\" onclick=\"\">app/Http/Controllers/ContactController.php:478-514</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.01362, "accumulated_duration_str": "13.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.607905, "duration": 0.01036, "duration_str": "10.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 76.065}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.636988, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 76.065, "width_percent": 5.58}, {"sql": "select * from `leads` where `id` = '19' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["19", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 484}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.642448, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:484", "source": "app/Http/Controllers/ContactController.php:484", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=484", "ajax": false, "filename": "ContactController.php", "line": "484"}, "connection": "omx_sass_systam_db", "start_percent": 81.645, "width_percent": 6.241}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` in (118)", "type": "query", "params": [], "bindings": ["118"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 484}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.655158, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:484", "source": "app/Http/Controllers/ContactController.php:484", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=484", "ajax": false, "filename": "ContactController.php", "line": "484"}, "connection": "omx_sass_systam_db", "start_percent": 87.885, "width_percent": 5.874}, {"sql": "select * from `pipelines` where `pipelines`.`id` in (30)", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 484}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.660294, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:484", "source": "app/Http/Controllers/ContactController.php:484", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=484", "ajax": false, "filename": "ContactController.php", "line": "484"}, "connection": "omx_sass_systam_db", "start_percent": 93.759, "width_percent": 6.241}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/api/leads/19", "status_code": "<pre class=sf-dump id=sf-dump-1213050056 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1213050056\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-824894674 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-824894674\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2145649071 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2145649071\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2088308949 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Im9VV082ejNTUGQwbThnQktNclc0WFE9PSIsInZhbHVlIjoiaFdFbjFhV2xBQnF5UGNPT09FSThnSXFvRmY1KzUzQWVuR3BZWEpndHAwdlJNQjdEWGd6S3RpeEZKaVE0ek5Ib1ZLdDQvQ2dMVGJPSkJjcEhLeDFabCswd2llY1hqWDIwTjAwVmxub1BzSlMrbmVia291OE5LZHYvYzBsNlc0dndMWFVhSEZYMVJGZTliSzJYbDduVUQyaDU2eXo2QWFEVlBsRGV6SVRpNFo1czFULzNVcW54Wlc5TzRUbTVsRGdmU0hzdk9xd0NvWDBWQnRYek1mcW9jU0RrSzJZQnhGTXRpR0FIMzdmMUNRKzB1b2hoZld5cnp5MDNXeXFoc2xwYUc1TEhHWE1aUCtyVE5scVJUSHFSVnlWbW9KWUJTM3BvcXlGOG5EMnZyZU5ISXRYeE1MWjVZcktCb2NYYXJRbFkxdXFQcmtpMk9NYkJqOHdmQ0NiRmoydUpveGFxeE5ONnN4ZVdpWWlpVWtqV3dJZFZEeS90RlZNellkWHpHNmtkLytMV3hrSE1IaTVFdEdna2UxTjNTM0JxWWs0ZnY2THVsakE3aU00djhHUUVoTFFNVUQrK1krTEw4cE81UW40ZE4xNC9ReS95ZWJlRDhmdXFqUU90VmNTVHZyMWJKTkI2d0hvWnVOd0VFRUlRUWJkeCtET1kxLytSNWNQZWpoZEQiLCJtYWMiOiJmMTY1OGYzZmU1YWI4NWE4YzkxZWJkYmE1MTA5MzAzMmIyYzhkODQzY2QwMTliNGI1MmE1ODRjMTM0M2RjYzdkIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjQvbDJRUSsyaG1PeFk1UktYTGtQM0E9PSIsInZhbHVlIjoiaTNYc0ZSNGVPZHhaTzdHbDZuUVh5a2FIbVZzdEZla2NiQUJWdzljL1Z1M1RhUU5BcllsRk9kSGhMZEJRR1FLTDhTd0p5UzZGT3pYVHpDcnF6SkZ5eWtGUGJxN2I4V1hoRmExa2l0UXlveU1QZ2xYb1BGRjZCaHhhSDJVNWZWY3B1ajVpTXZJTTVPYWE0Y2ZIc1k0WFlGM1N4U2J5RXoxdjkwSWdIdS9ETitWSllkNFprK0FqRVhSQzJCMHg3V09RamFFVWNNN1o0NWt4RTNqRndRa3ZDMk1tQnYwLzRqRkFoWnVvNVFWTFlIb3VoSnl4OWtuQ2VDSGJSMUJJc1UwaUdWZUhXZ203V1hUZUJwelIvTFdQdU9ZbGNZNHdSaE5PY1d6NWlubXgyZlVtVlQrOWc4Q1FqU1hoQ0dHdlhXdWRDTWNxUVVTbVJsTG0zcVFsR3cxd2twT2VnZ2xpdGZURWZmSDUxM3F1MVorK2xmMnpQekxYNVhhcVZTMml2YnlkRzJiV29yYmdiRitPVEZtU2RMaUdPa2ZKeUtjM0ZiWDZVeXhUQkd6aXY1RFNSdFlBVGFDZFpMUTBpc2NGN1ZqT3d2MzVWRlg1UnF4NlJtZStpY2hyV2FuVGN2Q3U5aXB1eFpoRzNOcjdvbC9OaGZHZDUrK2FrVlB1ZmtIKzNiYUMiLCJtYWMiOiIxYzI4NzUyMWJhYmZmNjU4MGY3YThkODg0NWI1MzAyZjY2MWFiMGMxNGIwNTlhMzJkNDNkZTYyYzIxZDIyM2U2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2088308949\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-379905173 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-379905173\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-166790986 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:12:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndmaVhWdk5OU0RicjlteWg0NklBN1E9PSIsInZhbHVlIjoiWDBDT2hmcG1SZTloQ1ZRZnh3NWxETEFKWUNpeVkrUFZqczZCcDlUMElHYTVzYWwza2NFVXhsRXR3d2VGS2RGZGJ2anM4ODM4aVI0VThnWDBHdkhONWRzWU42eUxwc2FVeHZlbFdadXRVTFh4V3laYTVRR0syY0M3UmJtUU1ndTEwc0poTUxtVmZTK2d3WXdnSHRLaGRiTnFWc1grNUhNZmgzR2xkVVlzRWRmV0tGYVZ5clB6ZG1ZLzF6b1Irb1daYk1maXV5ZjRQaUc5L2dDakNpVXA4akpJTEwvQk5HTUptR2NESkJmT284NU91T3V0NGIxWndOL0p4VXpKbjBleDZwZkF0clNqak5Gd24yNFNyK0t4YUx2QVFTZmRsWVR6bGFXaklnMkdYaWgvTTlpNE92TGJCemJiQXA4WUFQOVhSSnZRWldMOFhUS0RWNHdEZS82OXlpUGhFZFRqZHQ0OVNPbWJ6RkpJTTFQY0ZnV3lNWUNzVWpXZjJJR0xPMGxPanRQcUovTnRwN1F6dFl5MXlNdnJGY3dmU2lKNlJnUjAyS3oyaHp3c0VTQXNFbkE3WTExK0xaY1pYakN6ZWRNaWpCeHU2UkRZVm1vR2x0UVd4M3g5dWgyUU05RHhqcE85OFVJTEpEYmx0bDBvTFNxNk4vUTJtaXFpVy8vOUgrd0wiLCJtYWMiOiI4ZjQyYTc0Y2I2MWU0NzY3ZTQzODY0MjA1N2EyY2Q2Y2IxMzA1YWM5YThmOWNiYjFlZjU0MzkyMGY2NTU4YWMwIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:12:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlFsb0xxMVJCVkFKZzJWN0RvYWlsOUE9PSIsInZhbHVlIjoibjZUekw1bEJ3NWM4TFYybzFqck9ES003WXZxK1pKY2RnbTR2TXhwMGFzY2xrU29nM1Z3anlJcGNvbGRiMzErYisrcUtsMzBUWGFpRTRqaDhGc2MyS0hSejRkMDFEVEJwdW5rU2lMU0FHWWZiSFFEM0FjbFVJVUhZQnhKQmdmN1h1cWNOVVkvQ0kxR0hwUWVpa1Q3NHlUUnA1emlpTmRLNEZqRGRyY0RaajBnVyt6bXh0RXpnaS8vRVlGKzFuL0FVUzljZXQ4QjVhTTVRM2dlejBQRGF5REpadXVJQXAyb1RpaHFJQmN4ODczQVlSOE9hVXVBbkpTVnRYUTBmejQrdWZRSjFhRXRnUzVOM2kzdEhiZ250MWl0VlR6dGpmaktManVaK1Y3dkxGWllhZ3RjSU05VWE3eWhlL3crRXoxbDdmMXZPRnBkT0wrM1JhV0hHOC9yNVNNbE10a0VvS05Va2t3QlJVSTd3a0o1Z3lXT0hTY0ZCVjRhUllTbUM3blVpcjQ2K2MwaDhxalJ3WXBHbEVwc1RsNVYzb251MXVwMHRmbGdXVzkwVnJRbXNrQjdHMm5HZE9OdFJ6OGt6SFlUN1pOL2dDWGpzRVMvaWhCRFdYNmtnUEZWSWFqa2h4TUJZUFdYVS9OalBNeFhhY1ZjMStGMEdweVM5VEhySGpKcGUiLCJtYWMiOiIzZjQ1ZjZjOTk1NzdmYTVhODcwOTA3YmZjMjJjYjM1ZjJkZGM5MDQ2MWNjMDI0NzA4MmVlMjZkMWE1ODIzMGM5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:12:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndmaVhWdk5OU0RicjlteWg0NklBN1E9PSIsInZhbHVlIjoiWDBDT2hmcG1SZTloQ1ZRZnh3NWxETEFKWUNpeVkrUFZqczZCcDlUMElHYTVzYWwza2NFVXhsRXR3d2VGS2RGZGJ2anM4ODM4aVI0VThnWDBHdkhONWRzWU42eUxwc2FVeHZlbFdadXRVTFh4V3laYTVRR0syY0M3UmJtUU1ndTEwc0poTUxtVmZTK2d3WXdnSHRLaGRiTnFWc1grNUhNZmgzR2xkVVlzRWRmV0tGYVZ5clB6ZG1ZLzF6b1Irb1daYk1maXV5ZjRQaUc5L2dDakNpVXA4akpJTEwvQk5HTUptR2NESkJmT284NU91T3V0NGIxWndOL0p4VXpKbjBleDZwZkF0clNqak5Gd24yNFNyK0t4YUx2QVFTZmRsWVR6bGFXaklnMkdYaWgvTTlpNE92TGJCemJiQXA4WUFQOVhSSnZRWldMOFhUS0RWNHdEZS82OXlpUGhFZFRqZHQ0OVNPbWJ6RkpJTTFQY0ZnV3lNWUNzVWpXZjJJR0xPMGxPanRQcUovTnRwN1F6dFl5MXlNdnJGY3dmU2lKNlJnUjAyS3oyaHp3c0VTQXNFbkE3WTExK0xaY1pYakN6ZWRNaWpCeHU2UkRZVm1vR2x0UVd4M3g5dWgyUU05RHhqcE85OFVJTEpEYmx0bDBvTFNxNk4vUTJtaXFpVy8vOUgrd0wiLCJtYWMiOiI4ZjQyYTc0Y2I2MWU0NzY3ZTQzODY0MjA1N2EyY2Q2Y2IxMzA1YWM5YThmOWNiYjFlZjU0MzkyMGY2NTU4YWMwIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:12:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlFsb0xxMVJCVkFKZzJWN0RvYWlsOUE9PSIsInZhbHVlIjoibjZUekw1bEJ3NWM4TFYybzFqck9ES003WXZxK1pKY2RnbTR2TXhwMGFzY2xrU29nM1Z3anlJcGNvbGRiMzErYisrcUtsMzBUWGFpRTRqaDhGc2MyS0hSejRkMDFEVEJwdW5rU2lMU0FHWWZiSFFEM0FjbFVJVUhZQnhKQmdmN1h1cWNOVVkvQ0kxR0hwUWVpa1Q3NHlUUnA1emlpTmRLNEZqRGRyY0RaajBnVyt6bXh0RXpnaS8vRVlGKzFuL0FVUzljZXQ4QjVhTTVRM2dlejBQRGF5REpadXVJQXAyb1RpaHFJQmN4ODczQVlSOE9hVXVBbkpTVnRYUTBmejQrdWZRSjFhRXRnUzVOM2kzdEhiZ250MWl0VlR6dGpmaktManVaK1Y3dkxGWllhZ3RjSU05VWE3eWhlL3crRXoxbDdmMXZPRnBkT0wrM1JhV0hHOC9yNVNNbE10a0VvS05Va2t3QlJVSTd3a0o1Z3lXT0hTY0ZCVjRhUllTbUM3blVpcjQ2K2MwaDhxalJ3WXBHbEVwc1RsNVYzb251MXVwMHRmbGdXVzkwVnJRbXNrQjdHMm5HZE9OdFJ6OGt6SFlUN1pOL2dDWGpzRVMvaWhCRFdYNmtnUEZWSWFqa2h4TUJZUFdYVS9OalBNeFhhY1ZjMStGMEdweVM5VEhySGpKcGUiLCJtYWMiOiIzZjQ1ZjZjOTk1NzdmYTVhODcwOTA3YmZjMjJjYjM1ZjJkZGM5MDQ2MWNjMDI0NzA4MmVlMjZkMWE1ODIzMGM5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:12:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-166790986\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1510733428 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1510733428\", {\"maxDepth\":0})</script>\n"}}