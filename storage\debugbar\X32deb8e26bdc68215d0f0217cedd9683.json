{"__meta": {"id": "X32deb8e26bdc68215d0f0217cedd9683", "datetime": "2025-07-29 05:56:38", "utime": **********.956476, "method": "GET", "uri": "/api/leads/19", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753768597.979025, "end": **********.956497, "duration": 0.9774720668792725, "duration_str": "977ms", "measures": [{"label": "Booting", "start": 1753768597.979025, "relative_start": 0, "end": **********.829893, "relative_end": **********.829893, "duration": 0.8508682250976562, "duration_str": "851ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.829907, "relative_start": 0.8508820533752441, "end": **********.956499, "relative_end": 2.1457672119140625e-06, "duration": 0.12659215927124023, "duration_str": "127ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46064576, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/leads/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@getLeadForPreview", "namespace": null, "prefix": "", "where": [], "as": "api.leads.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=440\" onclick=\"\">app/Http/Controllers/ContactController.php:440-476</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.01661, "accumulated_duration_str": "16.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.891286, "duration": 0.01303, "duration_str": "13.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 78.447}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.919915, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 78.447, "width_percent": 5.238}, {"sql": "select * from `leads` where `id` = '19' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["19", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 446}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9254858, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "ContactController.php:446", "source": "app/Http/Controllers/ContactController.php:446", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=446", "ajax": false, "filename": "ContactController.php", "line": "446"}, "connection": "omx_sass_systam_db", "start_percent": 83.685, "width_percent": 6.623}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` in (118)", "type": "query", "params": [], "bindings": ["118"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 446}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.934976, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:446", "source": "app/Http/Controllers/ContactController.php:446", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=446", "ajax": false, "filename": "ContactController.php", "line": "446"}, "connection": "omx_sass_systam_db", "start_percent": 90.307, "width_percent": 4.275}, {"sql": "select * from `pipelines` where `pipelines`.`id` in (30)", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 446}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.939257, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:446", "source": "app/Http/Controllers/ContactController.php:446", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=446", "ajax": false, "filename": "ContactController.php", "line": "446"}, "connection": "omx_sass_systam_db", "start_percent": 94.582, "width_percent": 5.418}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/api/leads/19", "status_code": "<pre class=sf-dump id=sf-dump-392929770 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-392929770\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1787060059 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1787060059\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-292993745 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-292993745\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-307038982 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IkJnTFhwei9yUHBzeGZ5ckVBQTZqUEE9PSIsInZhbHVlIjoiNjhvb2V0L09DTXRKcEFiQkJwZ1hHUWFTVXZieUthR2tLYUdMWkpRZEtJbXMxZlMrbWpYdlpPTHZTK3JXbitxZFBhcHVHTHAyOURZaCtLS2RZU1lwL1U1NkxZVFlTMWlSK2RlZTlQUzB0ZWlPZ0lkT3FGcElFK0RlQVYzVW1HOXBXRWRRYkQzdS9jaUJQWmtrWHc2WGdzYkN3RDc2UzJxWHoyUFlrOEloT2VGY3Y5SFRPaGRocUN5UnAwUjhseWtWblgxUkJPa0JiOTd3dHl0V0RaZDZrcFJQTHZTckhoRTdqZEVMMFZhN202bFhYYXlZMHNBUlJBWlFhclpTcXBSdkFzUW9GU2pUL3pZa1ZyaW94WnllRWpoRkVvWjdlMUI5REExMFdDN2hDc0hwakpTSHVXS0VWZ1VpR0lOK01WNU80TjBmaE92NUo5Q0U3Sk04SzlFaDlEMnFNN0JqREVsdlZHOTJZczdsRFRhT2M1OGxMQnR1TzNodkRnMkdDMjIxRGNoNW1uYmlYWklJVVdDNWVqRDdrM0NiTUI0V1RtY2l5RzdPc1pQVTZ0d1BDdWhzYmE5UW1KakN2aDlnZmdaelJHODh3d0hacFBTRytNTWNSVThGWTltcXJVLzU3clpVU0VyVDk0cmE1S01pNnZ1YXVraHBoOGtkSzJ6a0tiOHIiLCJtYWMiOiJmZjUyMTE0NmVhMDRiNTQxZjgxYzAyZDM0MzY1NWUyMTRhMDA2NThkNWE4OTM0OTdiMDA5MTljOTk0OTYyNjVlIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im5nY3plNmNOcEIzS3dSNjAxQ0ljTVE9PSIsInZhbHVlIjoidWhMSnJNQ1JjNUg3b2JadFd5QTU1a3dpeU1kRU5OeXE1ajZLSFRSRkJrVDBQc2hUVUh5N3J5U2V5TWhDcTFQT3FITTFJblc0N0dHK2pPdHExaWx0RVJZdGhBRGQyUC9jeng3bS84ZUJicVJScVNnTWJrVTdKTmxUakpVMDB3eXVMNy9NTCs2a1hpbXpyb2hOaWhINDE0M1JRTEVIdFJ4M0Mybmpua2x5enpzeTdkeTM5cjdZM3dNd2h4SHMrNTQzZGo4RWpUZDFaREM2U3YvS1lPQVkrZ09qbmZqOW8rNWhBOVpVVDdZRkd5b3V2VWhLVFVuWVZ3SUl0YlZQNTZnK1Fqc09WUTNwV1pvUjVCa2htRlVnbHRveGVuTTdrTzZnekRraHNRSHR2am5QeHRpSSsyV2tQOUh1RVZwTGNQYnVIZ0NvcVl6RUhJdU4xcE1XZEQ4UFRkUmVVcWEySnNubXFDMjFuNmxRWnJ0M01LVFYzcVd0bEhZQWtKaDB1dDhZN1JOVTVLRzRCRjRQdTV5SERsdk9SdHE3UHAwTUdpOEU2Q3pxeHUxZ0UrN2V6bG5ybnlaai9TdXJTOUNuSVBRVFFaUmZySmo5RENKY3V6SE5JVTkwM3hWYlQ1ZmdTSDIxbStuTXlKRGJDL21MOVk1RVZnMi9tQU5WNGx2THd2YVMiLCJtYWMiOiI0NGFlZmFkNWEwMzg3YTQ0MDE0YWExMGZhNzkxN2RjYTM3NjkwZDY1YjQzODU5MWQ4MWUzOWY4YzU0YjZjYjE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-307038982\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-664638989 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-664638989\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:56:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImcxRmhwZzEyY3RqYlN6RWVyWjNnZmc9PSIsInZhbHVlIjoiL1NZVTNCcUsxclVMdThIZG0yaHdvSVpLY2JCUTdsVWNDbUJXbktqcm5XWmhoNmxBR3Q5ODJ4TzNHblhoTzdlMHE2OXZQdytDbkJCc0JnNzRDWFA2bFk3VHovSkhaeGxzRHlpSVIzcCsveHhQNVBqR0JzK1FIaHlvZkhtd2RHMW5xY2UyUiszVWZzM1hVR1pkdU51QTFkczBScHNCWHB3UFBlQnhubWd0bS81dWpLTW5IUmNYRUlZaDlPdnI2TU5Kd240dGVoS1NUam5ueHZvdkVTemxFL01ncTF6ZFdsSk5kZUk0SENNdy9Kb3UvdDdmN2ZhNU8wRjJPSW9GYWNoVUh2S3BXZktVdE1UT0Vadkt5ekJBVHlBckduU3NvNkNDdHhEcCswOFJEaHNtZFEwZHNmTFJaNVByVXFtMnRIRXJZK1dKcVlRREtjNjQvaEg4SXJqbVRYck5VODVlL2JIQUl2Ulc0WUt5dEVQUEd5K29vdFFGTUdrZGp0TW5sTG5WOXpZMWgwOHJCTWxCY3gwUittZU45ZTgyaHBUNWtyYVJrUmhVMmtFaXp3Vnh5WHFqSjJHRTNkd3RCTjEwb25NSkNicVRzaTlqclQycG1SdFZhR0taSUR6aXc4dEdyVFZFU2s1QWpJL1k5cVFXQXZIaitmRnJuNGQ4VkkyMTBNcUsiLCJtYWMiOiIyMWY4ZDk3OWJkMzk5ZjgxOTdiZWEzYzI1ZTQxZTEzYWIzYmU0NzRhNTc0NGNlZjYyYjA5MWFkOGRjZjcyOWE5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:56:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImErQ1NuOVpJeCs0T2hOVmI4ZXErRHc9PSIsInZhbHVlIjoiUWhsY3piaHVOLy9ucElGaFM2TGZmektEbjQyNE5PdndIcU9oQ2hmUTdDUm5CWGhEdnhFSnM1Qm84M1RQUVJFdmp3RXQ3YXhMd1RldGQ3dXpVVThvenhHejQ2bFc5TEJWK0xhc2EzVzNZNnByM2VwNW5OM1VNTUlpelBkdGFMNnhVUTArakU3ZDJZVmxXTVo5UGF0cU5peXlGbVcwYThqYll0Y0RmdlhuYzY3VzJxcGNDUU53VFNQUStvTVo0WmV0VmI2UUZsRDhmblRYQ3hFLy9mZm5UaXVDTWhiK0R3RDFHdlZnRmFnblhYU2NzZnZHalV6QStTYWZWaUpXclI2cEErUU41cEhTTEJ6WVFveTh1ellScC9qUFNrNTBjeGIrR005aEpzVElWWkJLSWdKOUJVRnZyRnBZTEpKTEJoWkZBd2k3SlZFckYxM1FFNmhoTk0xZ2ZqVmVJWnJFbjhlUGdMMEU2U2hkbVp5TkxpbDJpYThqVFhmUEp2M0U5VnBvZWZNM1FSK2RzR0ZvNERlV2xFZ3J5Vkh1RzFWa0NFd2dTbXdLRWFQZTFoajh6ZDZjcHNYSGlMbTlwbkZFbW1YNGRKUWNrNzA5UzE1RCtBMUdMTVJ5QUwwczN4cWlsazExUDFLYUdCcnl3UHZRVEZKZm9UTXBhV2Rwd0dTWkQ4TE4iLCJtYWMiOiI4OTRiNzA2MTA3M2IwMWE5NTAxNWVmMTdiZTM5M2VlYjBhMzAwYWUxNDYxMjU1MWUzMzNlNDEyOTYyODcyYjg2IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:56:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImcxRmhwZzEyY3RqYlN6RWVyWjNnZmc9PSIsInZhbHVlIjoiL1NZVTNCcUsxclVMdThIZG0yaHdvSVpLY2JCUTdsVWNDbUJXbktqcm5XWmhoNmxBR3Q5ODJ4TzNHblhoTzdlMHE2OXZQdytDbkJCc0JnNzRDWFA2bFk3VHovSkhaeGxzRHlpSVIzcCsveHhQNVBqR0JzK1FIaHlvZkhtd2RHMW5xY2UyUiszVWZzM1hVR1pkdU51QTFkczBScHNCWHB3UFBlQnhubWd0bS81dWpLTW5IUmNYRUlZaDlPdnI2TU5Kd240dGVoS1NUam5ueHZvdkVTemxFL01ncTF6ZFdsSk5kZUk0SENNdy9Kb3UvdDdmN2ZhNU8wRjJPSW9GYWNoVUh2S3BXZktVdE1UT0Vadkt5ekJBVHlBckduU3NvNkNDdHhEcCswOFJEaHNtZFEwZHNmTFJaNVByVXFtMnRIRXJZK1dKcVlRREtjNjQvaEg4SXJqbVRYck5VODVlL2JIQUl2Ulc0WUt5dEVQUEd5K29vdFFGTUdrZGp0TW5sTG5WOXpZMWgwOHJCTWxCY3gwUittZU45ZTgyaHBUNWtyYVJrUmhVMmtFaXp3Vnh5WHFqSjJHRTNkd3RCTjEwb25NSkNicVRzaTlqclQycG1SdFZhR0taSUR6aXc4dEdyVFZFU2s1QWpJL1k5cVFXQXZIaitmRnJuNGQ4VkkyMTBNcUsiLCJtYWMiOiIyMWY4ZDk3OWJkMzk5ZjgxOTdiZWEzYzI1ZTQxZTEzYWIzYmU0NzRhNTc0NGNlZjYyYjA5MWFkOGRjZjcyOWE5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:56:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImErQ1NuOVpJeCs0T2hOVmI4ZXErRHc9PSIsInZhbHVlIjoiUWhsY3piaHVOLy9ucElGaFM2TGZmektEbjQyNE5PdndIcU9oQ2hmUTdDUm5CWGhEdnhFSnM1Qm84M1RQUVJFdmp3RXQ3YXhMd1RldGQ3dXpVVThvenhHejQ2bFc5TEJWK0xhc2EzVzNZNnByM2VwNW5OM1VNTUlpelBkdGFMNnhVUTArakU3ZDJZVmxXTVo5UGF0cU5peXlGbVcwYThqYll0Y0RmdlhuYzY3VzJxcGNDUU53VFNQUStvTVo0WmV0VmI2UUZsRDhmblRYQ3hFLy9mZm5UaXVDTWhiK0R3RDFHdlZnRmFnblhYU2NzZnZHalV6QStTYWZWaUpXclI2cEErUU41cEhTTEJ6WVFveTh1ellScC9qUFNrNTBjeGIrR005aEpzVElWWkJLSWdKOUJVRnZyRnBZTEpKTEJoWkZBd2k3SlZFckYxM1FFNmhoTk0xZ2ZqVmVJWnJFbjhlUGdMMEU2U2hkbVp5TkxpbDJpYThqVFhmUEp2M0U5VnBvZWZNM1FSK2RzR0ZvNERlV2xFZ3J5Vkh1RzFWa0NFd2dTbXdLRWFQZTFoajh6ZDZjcHNYSGlMbTlwbkZFbW1YNGRKUWNrNzA5UzE1RCtBMUdMTVJ5QUwwczN4cWlsazExUDFLYUdCcnl3UHZRVEZKZm9UTXBhV2Rwd0dTWkQ4TE4iLCJtYWMiOiI4OTRiNzA2MTA3M2IwMWE5NTAxNWVmMTdiZTM5M2VlYjBhMzAwYWUxNDYxMjU1MWUzMzNlNDEyOTYyODcyYjg2IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:56:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}