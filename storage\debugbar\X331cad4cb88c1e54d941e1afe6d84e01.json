{"__meta": {"id": "X331cad4cb88c1e54d941e1afe6d84e01", "datetime": "2025-07-29 06:08:10", "utime": **********.703789, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753769289.589654, "end": **********.703818, "duration": 1.114164113998413, "duration_str": "1.11s", "measures": [{"label": "Booting", "start": 1753769289.589654, "relative_start": 0, "end": **********.590171, "relative_end": **********.590171, "duration": 1.0005171298980713, "duration_str": "1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.590189, "relative_start": 1.***************, "end": **********.703828, "relative_end": 1.0013580322265625e-05, "duration": 0.*****************, "duration_str": "114ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TIyKojmYFOrEoCSeKYQvAt961nZLyrdAfongIa0v", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-117886348 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-117886348\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-812416696 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-812416696\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1437103103 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1437103103\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1916412046 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1916412046\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-348637307 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-348637307\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1365921312 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:08:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpLZnRzVFk1aGozVVJjem9QcCthTWc9PSIsInZhbHVlIjoiUXJ1ZGtUVFoweit5Q0xGYTY0S1dwYnF1aGF2NkJhMm1mR1VkdThpaVFzOHNGRHpWM2kydUZNT2Zqell5Y2xTWUhQU3c3bmtOSnROUDAxL3p2cE00N0xRZEFUWlVrQUxPbUtjbTAvQ2RnU3ZONWFjVU5kUXNGbVRBOGR4aGR4Mnllc2w0a0FIRDlHVGRpZXF6Q1JzUUV3SkxIY3FBb04vSlkzR29rRmt1MDBoeHRmVWt5YWViSFJNRGdDdFl2VElkR2VZWmdsRkpWcE0yZ2djNHNERDlGYitwaTE3T0ZIUW1tYndycjdsN2xUZ3E1bUpVN0JxTlNyWXdKNFRlb0UzRStnNHdobXlxN2lOcG1iY25qZ0dlbDA3eVJ4M25nZ3JlWnhzVVg2eDdaUit5WXlJRys4NXFuK1QvQzQ2WjRyS2UyRVpDbEVSc3gvM3VaS1BOY0dGaHBYeldtVnFrWDRZa2YzVWVnTFpaRWZQM0piZ2RQMVFjaWJZWlQ3cXRUZmdZWUw0VVlNeXFEWGo5SDhFTThrVFNLU0NNNEtJNUNaek5YTThxY0xBYnJJUmlwa2Z1dStxVjN3U0FpVVZNL0JqK1lPcXB4cUNJM2RnTk1rNGJzRUtFOENjNjIwQ0RoYVBpYncyOWdvZEhRcG42RG5PeVRuWEVoSEFOQ0dicThXM0UiLCJtYWMiOiI4MGU3ZWQ4N2RlYTUwODczNGRjYjg0Mjk0ZWI3NTdlMWVhZjE0NDQwYTYxMjkyNDFiNjdkM2NkZDQyNjVkN2E0IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:08:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkQvV0FIUlBFVnVEeUtPWm5BNjFoSHc9PSIsInZhbHVlIjoiWkd1dEdzMUdZU0E5czl1ZWdwVzlPRjM3aEUyVWpXN0ZGS0lueEcySVh0L3NJMmJudEcrTXZWN3NxUWphYUpvVzFJb0xrb3o0dG1aRXltdWxvMk9pbXRBR2htelJ4VEF4dTJucG1GNUg5L28zbXBRVUxseG5XTElOZUNuOFZSYVdkcmRTZTFxVUMyQWtHbmNmcnBiMVZlczhkdWl6THNOMHgyN2NSWElqZE9iVWpvdTN1Rm04S25tN3Exb1lxOGRsejM3MXc1U2orYkdEb0V2c1JtSTBiVGdYZFlPcWY2K093aHdXa2pHYVVNT0s5ZGcyQy9vVG1oSGFrVC9GZHE2dDZlaVp0MHRCZUU4Qi8xVVlTZ0s0Rnp0Uk1ZRWVEODA1UFNURWN0LzJCOFlvYXBWQlFjN2xTVnFsN3h5akxxZitrbDRmSG1RMlc1Yzh6WTQ5YnBocXV6dkd1ZkFCdmw4VWp2MFpxb0FPMEphNW9tR1ZDWnlGOVZVUlVsVVZlc2MwcDdpOGEvcEFzcjFudTQzMXMxUkM3TDMvMGZjRVZPZDVqRFYvRVQ5TEd3cFNzdFJ6MmdTMjEzbmNFbUdIMXBGdkx3OGYvckNVdUlZOERMMlJxUTlCNzV5M2sxOUREYjhocUxabUtubllKRmU3a0loelhxTWgzbTVhRFlDeXVDdmIiLCJtYWMiOiIxNjIxY2FkNGFhMGI3ZjRhZmUxODY1YTU1NDM0MDk1OGViYjBkOTQ4OGE2ZDYxMjkxOTAwNGZhMjJhYTI2Y2UyIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:08:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpLZnRzVFk1aGozVVJjem9QcCthTWc9PSIsInZhbHVlIjoiUXJ1ZGtUVFoweit5Q0xGYTY0S1dwYnF1aGF2NkJhMm1mR1VkdThpaVFzOHNGRHpWM2kydUZNT2Zqell5Y2xTWUhQU3c3bmtOSnROUDAxL3p2cE00N0xRZEFUWlVrQUxPbUtjbTAvQ2RnU3ZONWFjVU5kUXNGbVRBOGR4aGR4Mnllc2w0a0FIRDlHVGRpZXF6Q1JzUUV3SkxIY3FBb04vSlkzR29rRmt1MDBoeHRmVWt5YWViSFJNRGdDdFl2VElkR2VZWmdsRkpWcE0yZ2djNHNERDlGYitwaTE3T0ZIUW1tYndycjdsN2xUZ3E1bUpVN0JxTlNyWXdKNFRlb0UzRStnNHdobXlxN2lOcG1iY25qZ0dlbDA3eVJ4M25nZ3JlWnhzVVg2eDdaUit5WXlJRys4NXFuK1QvQzQ2WjRyS2UyRVpDbEVSc3gvM3VaS1BOY0dGaHBYeldtVnFrWDRZa2YzVWVnTFpaRWZQM0piZ2RQMVFjaWJZWlQ3cXRUZmdZWUw0VVlNeXFEWGo5SDhFTThrVFNLU0NNNEtJNUNaek5YTThxY0xBYnJJUmlwa2Z1dStxVjN3U0FpVVZNL0JqK1lPcXB4cUNJM2RnTk1rNGJzRUtFOENjNjIwQ0RoYVBpYncyOWdvZEhRcG42RG5PeVRuWEVoSEFOQ0dicThXM0UiLCJtYWMiOiI4MGU3ZWQ4N2RlYTUwODczNGRjYjg0Mjk0ZWI3NTdlMWVhZjE0NDQwYTYxMjkyNDFiNjdkM2NkZDQyNjVkN2E0IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:08:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkQvV0FIUlBFVnVEeUtPWm5BNjFoSHc9PSIsInZhbHVlIjoiWkd1dEdzMUdZU0E5czl1ZWdwVzlPRjM3aEUyVWpXN0ZGS0lueEcySVh0L3NJMmJudEcrTXZWN3NxUWphYUpvVzFJb0xrb3o0dG1aRXltdWxvMk9pbXRBR2htelJ4VEF4dTJucG1GNUg5L28zbXBRVUxseG5XTElOZUNuOFZSYVdkcmRTZTFxVUMyQWtHbmNmcnBiMVZlczhkdWl6THNOMHgyN2NSWElqZE9iVWpvdTN1Rm04S25tN3Exb1lxOGRsejM3MXc1U2orYkdEb0V2c1JtSTBiVGdYZFlPcWY2K093aHdXa2pHYVVNT0s5ZGcyQy9vVG1oSGFrVC9GZHE2dDZlaVp0MHRCZUU4Qi8xVVlTZ0s0Rnp0Uk1ZRWVEODA1UFNURWN0LzJCOFlvYXBWQlFjN2xTVnFsN3h5akxxZitrbDRmSG1RMlc1Yzh6WTQ5YnBocXV6dkd1ZkFCdmw4VWp2MFpxb0FPMEphNW9tR1ZDWnlGOVZVUlVsVVZlc2MwcDdpOGEvcEFzcjFudTQzMXMxUkM3TDMvMGZjRVZPZDVqRFYvRVQ5TEd3cFNzdFJ6MmdTMjEzbmNFbUdIMXBGdkx3OGYvckNVdUlZOERMMlJxUTlCNzV5M2sxOUREYjhocUxabUtubllKRmU3a0loelhxTWgzbTVhRFlDeXVDdmIiLCJtYWMiOiIxNjIxY2FkNGFhMGI3ZjRhZmUxODY1YTU1NDM0MDk1OGViYjBkOTQ4OGE2ZDYxMjkxOTAwNGZhMjJhYTI2Y2UyIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:08:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1365921312\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1695478734 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TIyKojmYFOrEoCSeKYQvAt961nZLyrdAfongIa0v</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1695478734\", {\"maxDepth\":0})</script>\n"}}