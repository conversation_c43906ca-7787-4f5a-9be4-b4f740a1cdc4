{"__meta": {"id": "Xddb5b2de80371460e80d706098482b63", "datetime": "2025-07-29 05:57:00", "utime": **********.43797, "method": "PUT", "uri": "/contacts/leads/19", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753768619.609804, "end": **********.437991, "duration": 0.8281869888305664, "duration_str": "828ms", "measures": [{"label": "Booting", "start": 1753768619.609804, "relative_start": 0, "end": **********.270354, "relative_end": **********.270354, "duration": 0.6605501174926758, "duration_str": "661ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.270378, "relative_start": 0.660574197769165, "end": **********.437994, "relative_end": 3.0994415283203125e-06, "duration": 0.1676158905029297, "duration_str": "168ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46669320, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT contacts/leads/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@updateLead", "namespace": null, "prefix": "", "where": [], "as": "contacts.leads.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=378\" onclick=\"\">app/Http/Controllers/ContactController.php:378-404</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01646, "accumulated_duration_str": "16.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.317399, "duration": 0.0147, "duration_str": "14.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 89.307}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3428931, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 89.307, "width_percent": 5.164}, {"sql": "select * from `leads` where `id` = '19' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["19", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 382}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.349242, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:382", "source": "app/Http/Controllers/ContactController.php:382", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=382", "ajax": false, "filename": "ContactController.php", "line": "382"}, "connection": "omx_sass_systam_db", "start_percent": 94.471, "width_percent": 5.529}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/contacts/leads/19", "status_code": "<pre class=sf-dump id=sf-dump-1854954824 data-indent-pad=\"  \"><span class=sf-dump-num>422</span>\n</pre><script>Sfdump(\"sf-dump-1854954824\", {\"maxDepth\":0})</script>\n", "status_text": "Unprocessable Content", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1994989821 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1994989821\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-13459357 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>email</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>phone</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-13459357\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">549</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryAMWQoAYinXqtDAvA</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImcxRmhwZzEyY3RqYlN6RWVyWjNnZmc9PSIsInZhbHVlIjoiL1NZVTNCcUsxclVMdThIZG0yaHdvSVpLY2JCUTdsVWNDbUJXbktqcm5XWmhoNmxBR3Q5ODJ4TzNHblhoTzdlMHE2OXZQdytDbkJCc0JnNzRDWFA2bFk3VHovSkhaeGxzRHlpSVIzcCsveHhQNVBqR0JzK1FIaHlvZkhtd2RHMW5xY2UyUiszVWZzM1hVR1pkdU51QTFkczBScHNCWHB3UFBlQnhubWd0bS81dWpLTW5IUmNYRUlZaDlPdnI2TU5Kd240dGVoS1NUam5ueHZvdkVTemxFL01ncTF6ZFdsSk5kZUk0SENNdy9Kb3UvdDdmN2ZhNU8wRjJPSW9GYWNoVUh2S3BXZktVdE1UT0Vadkt5ekJBVHlBckduU3NvNkNDdHhEcCswOFJEaHNtZFEwZHNmTFJaNVByVXFtMnRIRXJZK1dKcVlRREtjNjQvaEg4SXJqbVRYck5VODVlL2JIQUl2Ulc0WUt5dEVQUEd5K29vdFFGTUdrZGp0TW5sTG5WOXpZMWgwOHJCTWxCY3gwUittZU45ZTgyaHBUNWtyYVJrUmhVMmtFaXp3Vnh5WHFqSjJHRTNkd3RCTjEwb25NSkNicVRzaTlqclQycG1SdFZhR0taSUR6aXc4dEdyVFZFU2s1QWpJL1k5cVFXQXZIaitmRnJuNGQ4VkkyMTBNcUsiLCJtYWMiOiIyMWY4ZDk3OWJkMzk5ZjgxOTdiZWEzYzI1ZTQxZTEzYWIzYmU0NzRhNTc0NGNlZjYyYjA5MWFkOGRjZjcyOWE5IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImErQ1NuOVpJeCs0T2hOVmI4ZXErRHc9PSIsInZhbHVlIjoiUWhsY3piaHVOLy9ucElGaFM2TGZmektEbjQyNE5PdndIcU9oQ2hmUTdDUm5CWGhEdnhFSnM1Qm84M1RQUVJFdmp3RXQ3YXhMd1RldGQ3dXpVVThvenhHejQ2bFc5TEJWK0xhc2EzVzNZNnByM2VwNW5OM1VNTUlpelBkdGFMNnhVUTArakU3ZDJZVmxXTVo5UGF0cU5peXlGbVcwYThqYll0Y0RmdlhuYzY3VzJxcGNDUU53VFNQUStvTVo0WmV0VmI2UUZsRDhmblRYQ3hFLy9mZm5UaXVDTWhiK0R3RDFHdlZnRmFnblhYU2NzZnZHalV6QStTYWZWaUpXclI2cEErUU41cEhTTEJ6WVFveTh1ellScC9qUFNrNTBjeGIrR005aEpzVElWWkJLSWdKOUJVRnZyRnBZTEpKTEJoWkZBd2k3SlZFckYxM1FFNmhoTk0xZ2ZqVmVJWnJFbjhlUGdMMEU2U2hkbVp5TkxpbDJpYThqVFhmUEp2M0U5VnBvZWZNM1FSK2RzR0ZvNERlV2xFZ3J5Vkh1RzFWa0NFd2dTbXdLRWFQZTFoajh6ZDZjcHNYSGlMbTlwbkZFbW1YNGRKUWNrNzA5UzE1RCtBMUdMTVJ5QUwwczN4cWlsazExUDFLYUdCcnl3UHZRVEZKZm9UTXBhV2Rwd0dTWkQ4TE4iLCJtYWMiOiI4OTRiNzA2MTA3M2IwMWE5NTAxNWVmMTdiZTM5M2VlYjBhMzAwYWUxNDYxMjU1MWUzMzNlNDEyOTYyODcyYjg2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-54796495 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-54796495\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:57:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBjYkZxNkpTRUdYWUJkSjRMUzN1cWc9PSIsInZhbHVlIjoicDU2bWR1MFFGOU5DWVRiT2xpN3lOZXh2ZFJDZUF1ZmxNbHBDeWw4OEtROVM1c0pjRzZIQ1NaRFVJMFRJRk5jdklLRngrU294eHplcjg1VzNqRTB1SXZhclhNVXRnRVdlZS9RVlg0eE1yRUt2aWpNVlFCMjlZbnprU2ZMeHo3aE1aWk9ia0tBaEE0L01HYkhvV3lScTUrd1d3ZkZPYTVkRjdlNVBtcXdab3NsTUJUajRYbjVrL0kzcU56b1NSZzdJMDNBYnJuZnRCRkxITEwvRlZrLzJBbWFvb1FSY3RBQTNlazR3MFZJaVRycCtTcDk0THAzTUNPeHpwMGJhVEhqQ2ZwVjJtWlpCK296ODM5MEErUWdORHpCblNtcWtYMUFUZGVpYU1EbC9MMHVzLzQyY0NIVWhFdSt6ZXNCYzBhZDcxR0RRdjVURnpnQ1pPY0lib0FyZ0NWRFBwcndWY25Fa2x3SnNlTGNWYmhvaUpydVdxVFREcGsrQWpaVFlnUzNCZTBybFlQLzRGRC9yQWVwTktYRE9mMmh1OUIyWlIxbm54QUNWTFZKUHA4QUVveC90a0lhWnFLMHhSWVdDRkJoYlQvbm1xbFM5ZXB6Y0NmUVNaUjlkcWs4UzVwNG5Oa0NsZXlVdkNsVHFHUTI1L01rQjk3eTllcmY0KzJHZFE0aVUiLCJtYWMiOiJhYmZiNTI1NWMzZDA2MTc4OWU2ZTJkNzhkZmMzOGNlYTM1ZWYzMTczNDdhZmFmNDNkNzE3YjQyZDU1MjFmNGJhIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:57:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ims2bnRSRlkyNmd5U0JVVEM0YkJpMXc9PSIsInZhbHVlIjoidUY0SzJlY1JURHJQaEROd01wMjNxdlZ6UWREVlYya0NJTlBuOHJjNWZTSGppazY4b2NNbFdHV1BwZXp0eGRCbDVycUM5YmpCZWsxVCtvMVBQZ1hDSjlDK3NDUTJZdlE5MFFXeHhvZHdwY0JRT1ZnaHlPeG55WHQ2RDdWQWVZcjdUbG1uNHZrUU1PRkVObmNFbVYwU1YzRFJhTFhmQ1RFOHU2VFlOdyt6dHpsNERxTzBqa3VjU243VmlOeWFJRGI5K29wdXRjajl3d0hPWVEvKzAvM3NwNmcvQ29LVHBkV1loQ1htbzhXcWJ0OWFCQXJuSFlpbkwrMm44MnVFa29ZSHE4d2pYTkVyRmQzTUxNb1A3azd3ekxiWjNncEtpemtvTHZGd3hLTk1wWGU4R1VnOUtEWSsrTjNrZC9YQlIrZ1owdmFYellKMVN1Vk1KY3hKUkFoNU5Uazl6YVUxNTZKalU3R1dIWldLa1o0bmFrZUc4UHpQV0xEZnhjbzQvTnVNMzFFU3QxYnFlOGxTdUgzZTE2R09xdGNicitKa003aEc2YkgwS05obHBwTzBVOVBCZjd3Nk90bk50enlSN0tPc3VhWlBjTCtHUElMYjB4K0NYSVk2MVN2ZjE4My9OTXFWaE1NSTFwczQ3SXpxMjFhT1M0bHZUMktaK1hYZlVqL2wiLCJtYWMiOiIxZTlmNjU0OWM2MzRjYzc4Nzk5NGU4MmZmZmFiMmM4YWQ3MGE5MmYzNTFkZTVlMjlhOTM4ZGI1ZDJiMDUwMjM5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:57:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBjYkZxNkpTRUdYWUJkSjRMUzN1cWc9PSIsInZhbHVlIjoicDU2bWR1MFFGOU5DWVRiT2xpN3lOZXh2ZFJDZUF1ZmxNbHBDeWw4OEtROVM1c0pjRzZIQ1NaRFVJMFRJRk5jdklLRngrU294eHplcjg1VzNqRTB1SXZhclhNVXRnRVdlZS9RVlg0eE1yRUt2aWpNVlFCMjlZbnprU2ZMeHo3aE1aWk9ia0tBaEE0L01HYkhvV3lScTUrd1d3ZkZPYTVkRjdlNVBtcXdab3NsTUJUajRYbjVrL0kzcU56b1NSZzdJMDNBYnJuZnRCRkxITEwvRlZrLzJBbWFvb1FSY3RBQTNlazR3MFZJaVRycCtTcDk0THAzTUNPeHpwMGJhVEhqQ2ZwVjJtWlpCK296ODM5MEErUWdORHpCblNtcWtYMUFUZGVpYU1EbC9MMHVzLzQyY0NIVWhFdSt6ZXNCYzBhZDcxR0RRdjVURnpnQ1pPY0lib0FyZ0NWRFBwcndWY25Fa2x3SnNlTGNWYmhvaUpydVdxVFREcGsrQWpaVFlnUzNCZTBybFlQLzRGRC9yQWVwTktYRE9mMmh1OUIyWlIxbm54QUNWTFZKUHA4QUVveC90a0lhWnFLMHhSWVdDRkJoYlQvbm1xbFM5ZXB6Y0NmUVNaUjlkcWs4UzVwNG5Oa0NsZXlVdkNsVHFHUTI1L01rQjk3eTllcmY0KzJHZFE0aVUiLCJtYWMiOiJhYmZiNTI1NWMzZDA2MTc4OWU2ZTJkNzhkZmMzOGNlYTM1ZWYzMTczNDdhZmFmNDNkNzE3YjQyZDU1MjFmNGJhIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:57:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ims2bnRSRlkyNmd5U0JVVEM0YkJpMXc9PSIsInZhbHVlIjoidUY0SzJlY1JURHJQaEROd01wMjNxdlZ6UWREVlYya0NJTlBuOHJjNWZTSGppazY4b2NNbFdHV1BwZXp0eGRCbDVycUM5YmpCZWsxVCtvMVBQZ1hDSjlDK3NDUTJZdlE5MFFXeHhvZHdwY0JRT1ZnaHlPeG55WHQ2RDdWQWVZcjdUbG1uNHZrUU1PRkVObmNFbVYwU1YzRFJhTFhmQ1RFOHU2VFlOdyt6dHpsNERxTzBqa3VjU243VmlOeWFJRGI5K29wdXRjajl3d0hPWVEvKzAvM3NwNmcvQ29LVHBkV1loQ1htbzhXcWJ0OWFCQXJuSFlpbkwrMm44MnVFa29ZSHE4d2pYTkVyRmQzTUxNb1A3azd3ekxiWjNncEtpemtvTHZGd3hLTk1wWGU4R1VnOUtEWSsrTjNrZC9YQlIrZ1owdmFYellKMVN1Vk1KY3hKUkFoNU5Uazl6YVUxNTZKalU3R1dIWldLa1o0bmFrZUc4UHpQV0xEZnhjbzQvTnVNMzFFU3QxYnFlOGxTdUgzZTE2R09xdGNicitKa003aEc2YkgwS05obHBwTzBVOVBCZjd3Nk90bk50enlSN0tPc3VhWlBjTCtHUElMYjB4K0NYSVk2MVN2ZjE4My9OTXFWaE1NSTFwczQ3SXpxMjFhT1M0bHZUMktaK1hYZlVqL2wiLCJtYWMiOiIxZTlmNjU0OWM2MzRjYzc4Nzk5NGU4MmZmZmFiMmM4YWQ3MGE5MmYzNTFkZTVlMjlhOTM4ZGI1ZDJiMDUwMjM5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:57:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-891189051 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-891189051\", {\"maxDepth\":0})</script>\n"}}