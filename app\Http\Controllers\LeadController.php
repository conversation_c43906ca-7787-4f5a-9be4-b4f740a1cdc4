<?php

namespace App\Http\Controllers;

use App\Mail\SendLeadEmail;
use App\Models\ClientDeal;
use App\Models\Deal;
use App\Models\DealCall;
use App\Models\DealDiscussion;
use App\Models\DealEmail;
use App\Models\DealFile;
use App\Models\Label;
use App\Models\Lead;
use App\Models\LeadActivityLog;
use App\Models\LeadCall;
use App\Models\LeadDiscussion;
use App\Models\LeadEmail;
use App\Models\LeadFile;
use App\Models\LeadStage;
use App\Models\Pipeline;
use App\Models\ProductService;
use App\Models\Source;
use App\Models\Stage;
use App\Models\User;
use App\Models\UserDeal;
use App\Models\UserLead;
use App\Models\LeadTask;
use App\Models\Utility;
use App\Models\WebhookSetting;
use App\Services\CrmWebhookDispatcher;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Spatie\Permission\Models\Role;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\LeadExport;
use App\Imports\LeadImport;
use App\Models\CustomField;
use App\Models\CustomFieldValue;
use App\Models\LeadCommentReaction;
class LeadController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if(!\Auth::user()->can('manage lead')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        if(\Auth::user()->default_pipeline) {
            $pipeline = Pipeline::where('created_by', '=', \Auth::user()->creatorId())
                              ->where('id', '=', \Auth::user()->default_pipeline)
                              ->first();
            if(!$pipeline) {
                $pipeline = Pipeline::where('created_by', '=', \Auth::user()->creatorId())->first();
            }
        } else {
            $pipeline = Pipeline::where('created_by', '=', \Auth::user()->creatorId())->first();
        }

        // Create default pipeline if none exists
        if(!$pipeline) {
            $pipeline = Pipeline::create([
                'name' => 'Default Pipeline',
                'created_by' => \Auth::user()->creatorId()
            ]);
            
            // Create default stages for the pipeline
            $defaultStages = ['New', 'Qualified', 'Discussion', 'Negotiation', 'Won/Lost'];
            foreach($defaultStages as $order => $stageName) {
                \App\Models\LeadStage::create([
                    'name' => $stageName,
                    'pipeline_id' => $pipeline->id,
                    'created_by' => \Auth::user()->creatorId(),
                    'order' => $order
                ]);
            }
        }

        $pipelines = Pipeline::where('created_by', '=', \Auth::user()->creatorId())->get()->pluck('name', 'id');
        
        // Get users for filter
        $currentUser = \Auth::user();
        $creatorId = ($currentUser->type == 'company' || $currentUser->type == 'super admin') 
            ? $currentUser->id 
            : $currentUser->created_by;
        
        $users = User::where(function($query) use ($creatorId, $currentUser) {
            // Include users created by the company owner
            $query->where('created_by', $creatorId)
                  // Include the company owner themselves  
                  ->orWhere('id', $creatorId);
        })
        ->where('type', '!=', 'client')
        ->where('is_active', '=', 1)
        ->select('id', 'name', 'email', 'type')
        ->orderBy('name', 'asc')
        ->get();

        // Ensure current user is included if not already
        $currentUserInList = $users->where('id', $currentUser->id)->first();
        if (!$currentUserInList) {
            $users = $users->push($currentUser);
            $users = $users->sortBy('name');
        }

        return view('leads.index', compact('pipelines', 'pipeline', 'users'));
    }

    public function lead_list()
    {
        $usr = \Auth::user();

        if($usr->can('manage lead'))
        {
            if($usr->default_pipeline)
            {
                $pipeline = Pipeline::where('created_by', '=', $usr->creatorId())->where('id', '=', $usr->default_pipeline)->first();
                if(!$pipeline)
                {
                    $pipeline = Pipeline::where('created_by', '=', $usr->creatorId())->first();
                }
            }
            else
            {
                $pipeline = Pipeline::where('created_by', '=', $usr->creatorId())->first();
            }

            $pipelines = Pipeline::where('created_by', '=', $usr->creatorId())->get()->pluck('name', 'id');
            $leads     = Lead::select('leads.*')
                ->join('user_leads', 'user_leads.lead_id', '=', 'leads.id')
                ->where('user_leads.user_id', '=', $usr->id)
                ->where('leads.pipeline_id', '=', $pipeline->id)
                ->where('leads.is_converted', '=', 0)
                ->where('leads.is_deleted', '=', 0)
                ->orderBy('leads.order')
                ->get();

            // Get enabled module integration for OMX Flow
            $omxFlowModule = \App\Models\ModuleIntegration::enabled()
                ->whereNotNull('base_url')
                ->where('base_url', '!=', '')
                ->first();

            return view('leads.list', compact('pipelines', 'pipeline', 'leads', 'omxFlowModule'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
{
    if (\Auth::user()->can('create lead')) {
        $creatorId = \Auth::user()->creatorId();

        $users = User::where('created_by', $creatorId)
            ->where('type', '!=', 'client')
            ->where('type', '!=', 'company')
            ->where('id', '!=', \Auth::user()->id)
            ->pluck('name', 'id');
        $users->prepend(__('Select User'), '');

        $pipelines = Pipeline::where('created_by', $creatorId)->pluck('name', 'id');
        $pipelines->prepend(__('Select Pipeline'), '');

        $sources = Source::where('created_by', $creatorId)->pluck('name', 'id');
        $products = ProductService::where('created_by', $creatorId)->pluck('name', 'id');

        // Get labels for tags/labels field
        $labels = \App\Models\Label::where('created_by', $creatorId)->pluck('name', 'id');
        
        // Create sample labels if none exist
        if ($labels->isEmpty()) {
            $sampleLabels = [
                ['name' => 'Hot Lead', 'color' => 'danger'],
                ['name' => 'Cold Lead', 'color' => 'secondary'],
                ['name' => 'Warm Lead', 'color' => 'warning'],
                ['name' => 'VIP', 'color' => 'primary'],
                ['name' => 'Follow Up', 'color' => 'info'],
                ['name' => 'New Customer', 'color' => 'success']
            ];
            
            foreach ($sampleLabels as $labelData) {
                \App\Models\Label::create([
                    'name' => $labelData['name'],
                    'color' => $labelData['color'],
                    'created_by' => $creatorId
                ]);
            }
            
            // Refresh labels after creating samples
            $labels = \App\Models\Label::where('created_by', $creatorId)->pluck('name', 'id');
        }
        
        // Debug: Log the labels being fetched
        \Log::info('Labels fetched for lead creation', [
            'creator_id' => $creatorId,
            'labels_count' => $labels->count(),
            'labels' => $labels->toArray()
        ]);

        $customFields = \App\Models\CustomField::where('module', 'Leads')->get();
        $customFieldValues = []; // Add this for create form

        return view('leads.create', compact(
            'users',
            'pipelines',
            'sources',
            'products',
            'labels',
            'customFields',
            'customFieldValues' // Add this
        ));
    } else {
        return response()->json(['error' => __('Permission Denied.')], 401);
    }
}
    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\Response
     */
    

    public function react(Request $request, $commentId)
    {
        $request->validate([
            'reaction' => 'required|string|max:16'
        ]);
    
        LeadCommentReaction::updateOrCreate(
            [
                'comment_id' => $commentId,
                'user_id' => auth()->id()
            ],
            [
                'reaction' => $request->reaction
            ]
        );
    
        return response()->json(['success' => true]);
    }
    
     public function store(Request $request)
    {
        try {
            // Add debugging
            \Log::info('Lead creation started', [
                'user_id' => \Auth::id(),
                'request_data' => $request->all()
            ]);

            $usr = \Auth::user();
            if($usr->can('create lead'))
            {
            $validator = \Validator::make(
                $request->all(), [
                                   'subject' => 'nullable',
                                   'name' => 'required',
                                   'email' => 'required|email',
                                   'phone' => 'nullable|string',
                                   'date_of_birth' => 'nullable|date',
                                   'type' => 'nullable|in:lead,customer',
                                   'status' => 'nullable|in:cold,warm,hot,won,lost',
                                   'opportunity_info' => 'nullable|string|max:255',
                                   'opportunity_description' => 'nullable|string',
                                   'opportunity_source' => 'nullable|in:website,referral,social_media,email,phone,advertisement,other',
                                   'lead_value' => 'nullable|numeric|min:0',
                                   'next_follow_up_date' => 'nullable|date',
                                   'labels' => 'nullable|array',
                                   'labels.*' => 'nullable|exists:labels,id',
                                   // Contact form fields (removed first_name, last_name - using 'name' field)
                                   'contact_type' => 'nullable|string|max:255',
                                   'tags' => 'nullable|string',
                                   'postal_code' => 'nullable|string|max:20',
                                   'city' => 'nullable|string|max:255',
                                   'state' => 'nullable|string|max:255',
                                   'country' => 'nullable|string|max:255',
                                   'business_name' => 'nullable|string|max:255',
                                   'business_gst' => 'nullable|string|max:255',
                                   'business_state' => 'nullable|string|max:255',
                                   'business_postal_code' => 'nullable|string|max:20',
                                   'business_address' => 'nullable|string',
                                   'dnd_settings' => 'nullable|string',
                               ]
            );

            if($validator->fails())
            {
                $messages = $validator->getMessageBag();

                // Check if this is an AJAX request
                if ($request->ajax()) {
                    return response()->json([
                        'success' => false,
                        'message' => $messages->first(),
                        'errors' => $messages->toArray()
                    ], 422);
                }

                return redirect()->back()->with('error', $messages->first());
            }

            // Pipeline and Stage Selection
            $pipeline = null;
            $stage = null;

            if($request->filled('pipeline_id') && $request->filled('stage_id'))
            {
                // Use provided pipeline and stage (from convert to lead modal or pipeline assignment)
                $pipeline = Pipeline::where('created_by', '=', $usr->creatorId())->where('id', '=', $request->pipeline_id)->first();
                $stage = LeadStage::where('id', '=', $request->stage_id)->where('created_by', '=', $usr->creatorId())->first();

                if(empty($pipeline))
                {
                    // Check if this is an AJAX request
                    if ($request->ajax()) {
                        return response()->json([
                            'success' => false,
                            'message' => __('Invalid pipeline selected.')
                        ], 400);
                    }

                    return redirect()->back()->with('error', __('Invalid pipeline selected.'));
                }

                if(empty($stage))
                {
                    // Check if this is an AJAX request
                    if ($request->ajax()) {
                        return response()->json([
                            'success' => false,
                            'message' => __('Invalid stage selected or no stages available for this pipeline.')
                        ], 400);
                    }

                    return redirect()->back()->with('error', __('Invalid stage selected or no stages available for this pipeline.'));
                }
            }
            else
            {
                // For new contacts without pipeline assignment, use default or first available
                if($usr->default_pipeline)
                {
                    $pipeline = Pipeline::where('created_by', '=', $usr->creatorId())->where('id', '=', $usr->default_pipeline)->first();
                    if(!$pipeline)
                    {
                        $pipeline = Pipeline::where('created_by', '=', $usr->creatorId())->first();
                    }
                }
                else
                {
                    $pipeline = Pipeline::where('created_by', '=', $usr->creatorId())->first();
                }

                if($pipeline) {
                    $stage = LeadStage::where('pipeline_id', '=', $pipeline->id)->first();
                }

                // If no pipeline or stage found, we'll create the lead without them
                // User can assign pipeline later using "Add Pipeline" feature
            }
            // End Pipeline and Stage Selection

            \Log::info('Creating lead with data', [
                'pipeline_id' => $pipeline ? $pipeline->id : null,
                'stage_id' => $stage ? $stage->id : null,
                'user_id' => $request->user_id
            ]);

            $lead              = new Lead();
            $lead->name        = $request->name;
            $lead->email       = $request->email;
            $lead->phone       = $request->phone;
            $lead->subject     = $request->subject ?? 'Lead from ' . $request->name; // Default subject if not provided
            $lead->user_id     = $request->user_id;
            $lead->pipeline_id = $pipeline ? $pipeline->id : null;
            $lead->stage_id    = $stage ? $stage->id : null;
            $lead->created_by  = $usr->creatorId();
            $lead->date        = date('Y-m-d');

                // Save existing fields
                $lead->date_of_birth = $request->date_of_birth;
                $lead->type = $request->type;
                $lead->status = $request->status ?? 'active';
                $lead->opportunity_info = $request->opportunity_info;
                $lead->opportunity_description = $request->opportunity_description;
                $lead->opportunity_source = $request->opportunity_source;
                $lead->lead_value = $request->lead_value;
                $lead->next_follow_up_date = $request->next_follow_up_date;

                // Save contact form fields (removed first_name, last_name - using 'name' field)
                $lead->contact_type = $request->contact_type ?? 'Lead';
                $lead->tags = $request->tags;
                $lead->postal_code = $request->postal_code;
                $lead->city = $request->city;
                $lead->state = $request->state;
                $lead->country = $request->country;
                $lead->business_name = $request->business_name;
                $lead->business_gst = $request->business_gst;
                $lead->business_state = $request->business_state;
                $lead->business_postal_code = $request->business_postal_code;
                $lead->business_address = $request->business_address;
                $lead->dnd_settings = $request->dnd_settings;
                
                // Handle labels
                if ($request->has('labels') && is_array($request->labels)) {
                    $labelIds = [];
                    
                    foreach ($request->labels as $label) {
                        if (strpos($label, 'new_') === 0) {
                            // This is a new tag, create it
                            $newLabelName = substr($label, 4); // Remove 'new_' prefix
                            
                            // Check if label already exists
                            $existingLabel = \App\Models\Label::where('name', $newLabelName)
                                ->where('created_by', $usr->creatorId())
                                ->first();
                            
                            if ($existingLabel) {
                                $labelIds[] = $existingLabel->id;
                            } else {
                                // Create new label
                                $newLabel = \App\Models\Label::create([
                                    'name' => $newLabelName,
                                    'color' => 'primary', // Default color
                                    'created_by' => $usr->creatorId()
                                ]);
                                $labelIds[] = $newLabel->id;
                            }
                        } else {
                            // This is an existing label ID
                            $labelIds[] = $label;
                        }
                    }
                    
                    if (!empty($labelIds)) {
                        $lead->labels = implode(',', $labelIds);
                    }
                }
                
                try {
                    $lead->save();
                    \Log::info('Lead saved successfully', ['lead_id' => $lead->id]);
                } catch (\Exception $e) {
                    \Log::error('Error saving lead', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    throw $e;
                }


                    if($request->user_id!=\Auth::user()->id){
                        $usrLeads = [
                            $usr->id,
                            $request->user_id,
                        ];
                    }else{
                        $usrLeads = [
                            $request->user_id,
                        ];
                    }

                try {
                    foreach($usrLeads as $usrLead)
                    {
                        UserLead::create(
                            [
                                'user_id' => $usrLead,
                                'lead_id' => $lead->id,
                            ]
                        );
                    }
                    \Log::info('UserLead relationships created successfully');
                } catch (\Exception $e) {
                    \Log::error('Error creating UserLead relationships', [
                        'error' => $e->getMessage(),
                        'lead_id' => $lead->id,
                        'user_leads' => $usrLeads
                    ]);
                    throw $e;
                }

                $leadArr = [
                    'lead_id' => $lead->id,
                    'name' => $lead->name,
                    'updated_by' => $usr->id,
                ];
                $lArr    = [
                    'lead_name' => $lead->name,
                    'lead_email' => $lead->email,
                    'lead_pipeline' => $pipeline->name,
                    'lead_stage' => $stage->name,
                ];

                $usrEmail = User::find($request->user_id);

                $lArr    = [
                    'lead_name' => $lead->name,
                    'lead_email' => $lead->email,
                    'lead_pipeline' => $pipeline->name,
                    'lead_stage' => $stage->name,
                ];

                // Send Email
                $setings = Utility::settings();
                if($setings['lead_assigned'] == 1)
                {
                    $usrEmail = User::find($request->user_id);
                    $leadAssignArr = [
                        'lead_name' => $lead->name,
                        'lead_email' => $lead->email,
                        'lead_subject' => $lead->subject,
                        'lead_pipeline' => $pipeline->name,
                        'lead_stage' => $stage->name,
                    ];
                    $resp = Utility::sendEmailTemplate('lead_assigned', [$usrEmail->id => $usrEmail->email], $leadAssignArr);
                }

                //For Notification
                $setting  = Utility::settings(\Auth::user()->creatorId());
                $leadArr = [
                    'user_name' => \Auth::user()->name,
                    'lead_name' => $lead->name,
                    'lead_email' => $lead->email,
                ];
                //Slack Notification
                if(isset($setting['lead_notification']) && $setting['lead_notification'] ==1)
                {
                    Utility::send_slack_msg('new_lead', $leadArr);
                }

                //Telegram Notification
                if(isset($setting['telegram_lead_notification']) && $setting['telegram_lead_notification'] ==1)
                {
                    Utility::send_telegram_msg('new_lead', $leadArr);
                }

                // Send webhook to integrated modules
                try {
                    $webhookDispatcher = new CrmWebhookDispatcher();
                    $webhookDispatcher->dispatchLeadCreated($lead);
                } catch (\Exception $e) {
                    \Log::error('Webhook dispatch failed', ['error' => $e->getMessage()]);
                }
                if ($request->has('custom_field')) {
                    foreach ($request->custom_field as $field_id => $value) {
                        CustomFieldValue::updateOrCreate(
                            [
                                'record_id' => $lead->id,
                                'field_id' => $field_id,
                            ],
                            [
                                'value' => is_array($value) ? json_encode($value) : $value,
                            ]
                        );
                    }
                }
                
                
                // Check if this is an AJAX request
                if ($request->ajax()) {
                    return response()->json([
                        'success' => true,
                        'message' => __('Lead successfully created!'),
                        'lead' => $lead
                    ]);
                }

                return redirect()->back()->with('success', __('Lead successfully created!') .((!empty ($resp) && $resp['is_success'] == false && !empty($resp['error'])) ? '<br> <span class="text-danger">' . $resp['error'] . '</span>' : ''));
            }
            else
            {
                // Check if this is an AJAX request
                if ($request->ajax()) {
                    return response()->json([
                        'success' => false,
                        'message' => __('Permission Denied.')
                    ], 403);
                }

                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        } catch (\Exception $e) {
            \Log::error('Error in lead creation', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Check if this is an AJAX request
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Server error: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'Server error: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Lead $lead
     *
     * @return \Illuminate\Http\Response
     */
  
     

    
     public function show(Lead $lead)
     {
         if ($lead->is_active) {
             $calenderTasks = [];
             $deal = Deal::where('id', $lead->is_converted)->first();
             $stageCnt = LeadStage::where('pipeline_id', $lead->pipeline_id)
                                 ->where('created_by', $lead->created_by)->get();
     
             $i = 0;
             foreach ($stageCnt as $stage) {
                 $i++;
                 if ($stage->id == $lead->stage_id) {
                     break;
                 }
             }
     
             $precentage = number_format(($i * 100) / max(count($stageCnt), 1));
             
             // Load custom fields
             $customFields = CustomField::where('module', 'Leads')->get();
             $customFieldValues = CustomField::getData($lead, 'Leads');
             
             // Add users for mentions
             $mentionUsers = User::where('created_by', \Auth::user()->creatorId())
                 ->select('id', 'name')
                 ->get();
             
             // Load comments with user relationship
             $lead->load(['comments.user']);
     
             return view('leads.show', compact(
                 'lead',
                 'calenderTasks',
                 'deal',
                 'precentage',
                 'customFields',
                 'customFieldValues',
                 'mentionUsers'
             ));
         } else {
             return redirect()->back()->with('error', __('Permission Denied.'));
         }
     }   
    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Lead $lead
     *
     * @return \Illuminate\Http\Response
     */
    public function edit(Lead $lead)
{
    if(\Auth::user()->can('edit lead'))
    {
        if($lead->created_by == \Auth::user()->creatorId())
        {
            $pipelines = Pipeline::where('created_by', '=', \Auth::user()->creatorId())->get()->pluck('name', 'id');
            $pipelines->prepend(__('Select Pipeline'), '');

            $sources        = Source::where('created_by', '=', \Auth::user()->creatorId())->get()->pluck('name', 'id');
            $products       = ProductService::where('created_by', '=', \Auth::user()->creatorId())->get()->pluck('name', 'id');
            $users          = User::where('created_by', '=', \Auth::user()->creatorId())
                                  ->where('type', '!=', 'client')
                                  ->where('type', '!=', 'company')
                                  ->where('id', '!=', \Auth::user()->id)
                                  ->get()->pluck('name', 'id');

            $lead->sources  = explode(',', $lead->sources);
            $lead->products = explode(',', $lead->products);

            // ✅ Add custom fields
            $customFields = CustomField::where('module', 'Leads')->get();
            $customFieldValues = CustomField::getData($lead, 'Leads');

            // 🟢 Add stages for the current pipeline
            $stages = \App\Models\LeadStage::where('pipeline_id', $lead->pipeline_id)
                ->where('created_by', \Auth::user()->creatorId())
                ->orderBy('order')
                ->pluck('name', 'id');
            $stages->prepend(__('Select Stage'), '');

            return view('leads.edit', compact(
                'lead',
                'pipelines',
                'sources',
                'products',
                'users',
                'customFields',
                'customFieldValues',
                'stages' // 🟢 pass stages to the view
            ));
        }
        else
        {
            return response()->json(['error' => __('Permission Denied.')], 401);
        }
    }
    else
    {
        return response()->json(['error' => __('Permission Denied.')], 401);
    }
}


    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Lead $lead
     *
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Lead $lead)
{
    if (\Auth::user()->can('edit lead')) {
        if ($lead->created_by == \Auth::user()->creatorId()) {
            $validator = \Validator::make(
                $request->all(),
                [
                    'subject'     => 'required',
                    'name'        => 'required',
                    'email'       => 'required|email',
                    'pipeline_id' => 'required',
                    'user_id'     => 'required',
                    'stage_id'    => 'required',
                    'sources'     => 'required',
                    'products'    => 'required',
                    // Add validation for next_follow_up_date
                    'next_follow_up_date' => 'nullable|date',
                ]
            );

            if ($validator->fails()) {
                $messages = $validator->getMessageBag();
                return redirect()->back()->with('error', $messages->first());
            }

            // Store original values for change tracking
            $originalStageId = $lead->stage_id;
            $originalUserId = $lead->user_id;

            $lead->name        = $request->name;
            $lead->email       = $request->email;
            $lead->phone       = $request->phone;
            $lead->subject     = $request->subject;
            $lead->user_id     = $request->user_id;
            $lead->pipeline_id = $request->pipeline_id;
            $lead->stage_id    = $request->stage_id;
            $lead->sources     = implode(",", array_filter($request->sources));
            $lead->products    = implode(",", array_filter($request->products));
            $lead->notes       = $request->notes;
            // Save next_follow_up_date if provided
            $lead->next_follow_up_date = $request->next_follow_up_date;
            $lead->save();

            // Send webhooks for changes
            try {
                $webhookDispatcher = new CrmWebhookDispatcher();

                // Send lead updated webhook
                $webhookDispatcher->dispatchLeadUpdated($lead);

                // Send stage changed webhook if stage changed
                if ($originalStageId != $request->stage_id) {
                    $webhookDispatcher->dispatchLeadStageChanged($lead, $originalStageId, $request->stage_id);
                }

                // Send assignment webhooks if user changed
                if ($originalUserId != $request->user_id) {
                    if ($originalUserId && !$request->user_id) {
                        // Lead was unassigned
                        $webhookDispatcher->dispatch('crm.lead_unassigned', $lead, ['previous_user_id' => $originalUserId]);
                    } elseif (!$originalUserId && $request->user_id) {
                        // Lead was assigned
                        $webhookDispatcher->dispatch('crm.lead_assigned', $lead, ['assigned_user_id' => $request->user_id]);
                    } elseif ($originalUserId && $request->user_id) {
                        // Lead was reassigned
                        $webhookDispatcher->dispatch('crm.lead_assigned', $lead, [
                            'assigned_user_id' => $request->user_id,
                            'previous_user_id' => $originalUserId
                        ]);
                    }
                }
            } catch (\Exception $e) {
                \Log::error('Webhook dispatch failed for lead update', ['error' => $e->getMessage()]);
            }

            // ✅ Save Custom Fields (if any)
            if ($request->has('custom_field')) {
                foreach ($request->custom_field as $field_id => $value) {
                    // If multiple checkboxes were selected, encode as JSON
                    if (is_array($value)) {
                        $value = json_encode($value);
                    }

                    \App\Models\CustomFieldValue::updateOrCreate(
                        [
                            'record_id' => $lead->id,
                            'field_id'  => $field_id,
                        ],
                        [
                            'value' => $value,
                        ]
                    );
                }
            }

            return redirect()->back()->with('success', __('Lead successfully updated!'));
        } else {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    } else {
        return redirect()->back()->with('error', __('Permission Denied.'));
    }
}

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Lead $lead
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(Lead $lead)
    {
        if(\Auth::user()->can('delete lead'))
        {
            if($lead->created_by == \Auth::user()->creatorId())
            {
                // Send webhook before deleting
                try {
                    $webhookDispatcher = new CrmWebhookDispatcher();
                    $webhookDispatcher->dispatchLeadDeleted($lead);
                } catch (\Exception $e) {
                    \Log::error('Webhook dispatch failed for lead deletion', ['error' => $e->getMessage()]);
                }

                LeadDiscussion::where('lead_id', '=', $lead->id)->delete();
                LeadFile::where('lead_id', '=', $lead->id)->delete();
                UserLead::where('lead_id', '=', $lead->id)->delete();
                LeadActivityLog::where('lead_id', '=', $lead->id)->delete();
                $lead->delete();

                return redirect()->back()->with('success', __('Lead successfully deleted!'));
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    public function json(Request $request)
    {
        $lead_stages = new LeadStage();
        if($request->pipeline_id && !empty($request->pipeline_id))
        {


            $lead_stages = $lead_stages->where('pipeline_id', '=', $request->pipeline_id);
            $lead_stages = $lead_stages->get()->pluck('name', 'id');
        }
        else
        {
            $lead_stages = [];
        }

        return response()->json($lead_stages);
    }

    public function fileUpload($id, Request $request)
    {
        try {
            // Check permissions
            if(!\Auth::user()->can('edit lead')) {
                return response()->json([
                    'is_success' => false,
                    'error' => __('Permission Denied.'),
                ], 401);
            }

            // Find the lead
            $lead = Lead::find($id);
            if(!$lead) {
                return response()->json([
                    'is_success' => false,
                    'error' => __('Lead not found.'),
                ], 404);
            }

            // Check if user owns the lead
            if($lead->created_by != \Auth::user()->creatorId()) {
                return response()->json([
                    'is_success' => false,
                    'error' => __('Permission Denied.'),
                ], 401);
            }

            // Validate the file
            $request->validate([
                'file' => 'required|file|max:20480', // 20MB max
            ]);

            if(!$request->hasFile('file')) {
                return response()->json([
                    'is_success' => false,
                    'error' => __('No file uploaded.'),
                ], 400);
            }

            $uploadedFile = $request->file('file');

            // Check storage limit
            $image_size = $uploadedFile->getSize();
            $creatorId = \Auth::user()->creatorId();

            // Validate creator exists before checking storage limit
            if (!$creatorId) {
                return response()->json([
                    'is_success' => false,
                    'error' => __('Invalid user session.'),
                ], 401);
            }

            $result = Utility::updateStorageLimit($creatorId, $image_size);

            // Use the same approach as project files
            $originalName = $uploadedFile->getClientOriginalName();
            $extension = $uploadedFile->getClientOriginalExtension();
            $fileName = \Illuminate\Support\Str::uuid() . '.' . $extension;

            // Store file using public disk like project files
            $filePath = $uploadedFile->storeAs('lead_files/' . $id, $fileName, 'public');

            // Create file record using the same structure as project files
            $file = LeadFile::create([
                'lead_id' => $id,
                'file_name' => pathinfo($originalName, PATHINFO_FILENAME),
                'original_name' => $originalName,
                'file_path' => $filePath,
                'file_type' => $extension,
                'mime_type' => $uploadedFile->getMimeType(),
                'file_size' => $image_size,
                'uploaded_by' => \Auth::user()->id
            ]);

            $return = [
                'is_success' => true,
                'uploaded_file' => [
                    'id' => $file->id,
                    'file_name' => $originalName,
                    'file_path' => $filePath,
                    'size' => $image_size,
                    'formatted_size' => $file->formatted_size,
                    'ext' => $extension,
                    'created_at' => $file->created_at->format('Y-m-d H:i:s'),
                    'download_url' => route('leads.file.download', [$lead->id, $file->id]),
                    'delete_url' => route('leads.file.delete', [$lead->id, $file->id]),
                    'can_delete' => \Auth::user()->can('edit lead')
                ]
            ];

            if($result == 1) {
                $return['download'] = route('leads.file.download', [$lead->id, $file->id]);
                $return['delete'] = route('leads.file.delete', [$lead->id, $file->id]);
            } else {
                // Storage limit exceeded or other error
                if (is_string($result) && $result != 1) {
                    // For now, let's allow the upload but show a warning
                    // This prevents the "Plan not found" error from blocking uploads
                    if (strpos($result, 'Plan not found') !== false || strpos($result, 'Storage limit check failed') !== false) {
                        \Log::warning('Storage limit check failed, allowing upload anyway', [
                            'result' => $result,
                            'user_id' => $creatorId,
                            'file_name' => $originalName
                        ]);

                        $return['download'] = route('leads.file.download', [$lead->id, $file->id]);
                        $return['delete'] = route('leads.file.delete', [$lead->id, $file->id]);
                        $return['status'] = 1;
                        $return['success_msg'] = '<br> <span class="text-warning">File uploaded but storage limit check failed.</span>';
                    } else {
                        // Delete the file record and file since storage failed
                        \Illuminate\Support\Facades\Storage::disk('public')->delete($filePath);
                        $file->delete();

                        return response()->json([
                            'is_success' => false,
                            'error' => $result,
                        ], 400);
                    }
                } else {
                    $return['status'] = 1;
                    $return['success_msg'] = '<br> <span class="text-danger">' . $result . '</span>';
                }
            }

            // Log the activity
            LeadActivityLog::create([
                'user_id' => \Auth::user()->id,
                'lead_id' => $lead->id,
                'log_type' => 'Upload File',
                'remark' => json_encode(['file_name' => $originalName]),
            ]);

            return response()->json($return);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'is_success' => false,
                'error' => $e->validator->errors()->first(),
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Lead file upload error: ' . $e->getMessage(), [
                'lead_id' => $id,
                'user_id' => \Auth::user()->id,
                'creator_id' => \Auth::user()->creatorId(),
                'file_name' => $request->file('file') ? $request->file('file')->getClientOriginalName() : 'no file',
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'is_success' => false,
                'error' => __('File upload failed. Please try again.') . ' Error: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function fileDownload($id, $file_id)
    {
        if(\Auth::user()->can('edit lead'))
        {
            $lead = Lead::find($id);
            if($lead->created_by == \Auth::user()->creatorId())
            {
                $file = LeadFile::find($file_id);
                if($file)
                {
                    // Use Laravel Storage like project files
                    if (!\Illuminate\Support\Facades\Storage::disk('public')->exists($file->file_path)) {
                        return redirect()->back()->with('error', __('File not found.'));
                    }

                    $filename = $file->original_name ?? $file->file_name;
                    return \Illuminate\Support\Facades\Storage::disk('public')->download($file->file_path, $filename);
                }
                else
                {
                    return redirect()->back()->with('error', __('File is not exist.'));
                }
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    public function fileDelete($id, $file_id)
    {
        try {
            if(!\Auth::user()->can('edit lead'))
            {
                return response()->json([
                    'is_success' => false,
                    'error' => __('Permission Denied.'),
                ], 401);
            }

            $lead = Lead::find($id);
            if(!$lead || $lead->created_by != \Auth::user()->creatorId())
            {
                return response()->json([
                    'is_success' => false,
                    'error' => __('Lead not found or access denied.'),
                ], 404);
            }

            $file = LeadFile::find($file_id);
            if(!$file)
            {
                return response()->json([
                    'is_success' => false,
                    'error' => __('File not found.'),
                ], 404);
            }

            // Delete file from storage if it exists
            if (\Illuminate\Support\Facades\Storage::disk('public')->exists($file->file_path)) {
                \Illuminate\Support\Facades\Storage::disk('public')->delete($file->file_path);
            }

            // Delete file record from database
            $file->delete();

            // Log the activity
            LeadActivityLog::create([
                'user_id' => \Auth::user()->id,
                'lead_id' => $lead->id,
                'log_type' => 'Delete File',
                'remark' => json_encode(['file_name' => $file->original_name ?? $file->file_name]),
            ]);

            return response()->json([
                'is_success' => true,
                'message' => __('File deleted successfully.')
            ], 200);

        } catch (\Exception $e) {
            \Log::error('Lead file delete error: ' . $e->getMessage(), [
                'lead_id' => $id,
                'file_id' => $file_id,
                'user_id' => \Auth::user()->id,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'is_success' => false,
                'error' => __('Failed to delete file. Please try again.'),
            ], 500);
        }
    }

    public function noteStore($id, Request $request)
    {
        if(\Auth::user()->can('edit lead'))
        {
            $lead = Lead::find($id);
            if($lead->created_by == \Auth::user()->creatorId())
            {
                $lead->notes = $request->notes;
                $lead->save();

                return response()->json(
                    [
                        'is_success' => true,
                        'success' => __('Note successfully saved!'),
                    ], 200
                );
            }
            else
            {
                return response()->json(
                    [
                        'is_success' => false,
                        'error' => __('Permission Denied.'),
                    ], 401
                );
            }
        }
        else
        {
            return response()->json(
                [
                    'is_success' => false,
                    'error' => __('Permission Denied.'),
                ], 401
            );
        }
    }

    public function labels($id)
    {
        if(\Auth::user()->can('edit lead'))
        {
            $lead = Lead::find($id);
            if($lead->created_by == \Auth::user()->creatorId())
            {
                $labels   = Label::where('pipeline_id', '=', $lead->pipeline_id)->where('created_by', \Auth::user()->creatorId())->get();
                $selected = $lead->labels();
                if($selected)
                {
                    $selected = $selected->pluck('name', 'id')->toArray();
                }
                else
                {
                    $selected = [];
                }

                return view('leads.labels', compact('lead', 'labels', 'selected'));
            }
            else
            {
                return response()->json(['error' => __('Permission Denied.')], 401);
            }
        }
        else
        {
            return response()->json(['error' => __('Permission Denied.')], 401);
        }
    }

    public function labelStore($id, Request $request)
    {
        if(\Auth::user()->can('edit lead'))
        {
            $leads = Lead::find($id);
            if($leads->created_by == \Auth::user()->creatorId())
            {
                $selectedLabels = $request->labels ? array_filter($request->labels) : [];

                // Handle new label creation
                if($request->new_label_name && $request->new_label_color)
                {
                    $validator = \Validator::make($request->all(), [
                        'new_label_name' => 'required|max:20',
                        'new_label_color' => 'required',
                    ]);

                    if($validator->fails())
                    {
                        $messages = $validator->getMessageBag();
                        return redirect()->back()->with('error', $messages->first());
                    }

                    // Create new label
                    $newLabel = new Label();
                    $newLabel->name = $request->new_label_name;
                    $newLabel->color = $request->new_label_color;
                    $newLabel->pipeline_id = $leads->pipeline_id;
                    $newLabel->created_by = \Auth::user()->ownerId();
                    $newLabel->save();

                    // Add the new label to selected labels
                    $selectedLabels[] = $newLabel->id;
                }

                // Update lead labels
                if(!empty($selectedLabels))
                {
                    $leads->labels = implode(",", $selectedLabels);
                }
                else
                {
                    $leads->labels = null;
                }
                $leads->save();

                return redirect()->back()->with('success', __('Labels successfully updated!'));
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    public function userEdit($id)
    {
        if(\Auth::user()->can('edit lead'))
        {
            $lead = Lead::find($id);

            if($lead->created_by == \Auth::user()->creatorId())
            {
                $users = User::where('created_by', '=', \Auth::user()->creatorId())->where('type', '!=', 'client')->where('type', '!=', 'company')->whereNOTIn(
                    'id', function ($q) use ($lead){
                    $q->select('user_id')->from('user_leads')->where('lead_id', '=', $lead->id);
                }
                )->get();


                $users = $users->pluck('name', 'id');

                return view('leads.users', compact('lead', 'users'));
            }
            else
            {
                return response()->json(['error' => __('Permission Denied.')], 401);
            }
        }
        else
        {
            return response()->json(['error' => __('Permission Denied.')], 401);
        }
    }

    public function userUpdate($id, Request $request)
    {
        if(\Auth::user()->can('edit lead'))
        {
            $usr  = \Auth::user();
            $lead = Lead::find($id);

            if($lead->created_by == $usr->creatorId())
            {
                if(!empty($request->users))
                {
                    $users   = array_filter($request->users);
                    $leadArr = [
                        'lead_id' => $lead->id,
                        'name' => $lead->name,
                        'updated_by' => $usr->id,
                    ];

                    foreach($users as $user)
                    {
                        UserLead::create(
                            [
                                'lead_id' => $lead->id,
                                'user_id' => $user,
                            ]
                        );
                    }
                }

                if(!empty($users) && !empty($request->users))
                {
                    return redirect()->back()->with('success', __('Users successfully updated!'));
                }
                else
                {
                    return redirect()->back()->with('error', __('Please Select Valid User!'));
                }
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    public function userDestroy($id, $user_id)
    {
        if(\Auth::user()->can('edit lead'))
        {
            $lead = Lead::find($id);
            if($lead->created_by == \Auth::user()->creatorId())
            {
                UserLead::where('lead_id', '=', $lead->id)->where('user_id', '=', $user_id)->delete();

                return redirect()->back()->with('success', __('User successfully deleted!'));
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    public function productEdit($id)
    {
        if(\Auth::user()->can('edit lead'))
        {
            $lead = Lead::find($id);
            if($lead->created_by == \Auth::user()->creatorId())
            {
                $products = ProductService::where('created_by', '=', \Auth::user()->creatorId())->whereNOTIn('id', explode(',', $lead->products))->get()->pluck('name', 'id');

                return view('leads.products', compact('lead', 'products'));
            }
            else
            {
                return response()->json(['error' => __('Permission Denied.')], 401);
            }
        }
        else
        {
            return response()->json(['error' => __('Permission Denied.')], 401);
        }
    }

    public function productUpdate($id, Request $request)
    {
        if(\Auth::user()->can('edit lead'))
        {
            $usr        = \Auth::user();
            $lead       = Lead::find($id);
            $lead_users = $lead->users->pluck('id')->toArray();

            if($lead->created_by == \Auth::user()->creatorId())
            {
                if(!empty($request->products))
                {
                    $products       = array_filter($request->products);
                    $old_products   = explode(',', $lead->products);
                    $lead->products = implode(',', array_merge($old_products, $products));
                    $lead->save();

                    $objProduct = ProductService::whereIN('id', $products)->get()->pluck('name', 'id')->toArray();

                    LeadActivityLog::create(
                        [
                            'user_id' => $usr->id,
                            'lead_id' => $lead->id,
                            'log_type' => 'Add Product',
                            'remark' => json_encode(['title' => implode(",", $objProduct)]),
                        ]
                    );

                    $productArr = [
                        'lead_id' => $lead->id,
                        'name' => $lead->name,
                        'updated_by' => $usr->id,
                    ];

                }

                if(!empty($products) && !empty($request->products))
                {
                    return redirect()->back()->with('success', __('Products successfully updated!'))->with('status', 'products');
                }
                else
                {
                    return redirect()->back()->with('error', __('Please Select Valid Product!'))->with('status', 'general');
                }
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'))->with('status', 'products');
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'))->with('status', 'products');
        }
    }

    public function productDestroy($id, $product_id)
    {
        if(\Auth::user()->can('edit lead'))
        {
            $lead = Lead::find($id);
            if($lead->created_by == \Auth::user()->creatorId())
            {
                $products = explode(',', $lead->products);
                foreach($products as $key => $product)
                {
                    if($product_id == $product)
                    {
                        unset($products[$key]);
                    }
                }
                $lead->products = implode(',', $products);
                $lead->save();

                return redirect()->back()->with('success', __('Products successfully deleted!'))->with('status', 'products');
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'))->with('status', 'products');
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'))->with('status', 'products');
        }
    }

    public function sourceEdit($id)
    {
        if(\Auth::user()->can('edit lead'))
        {
            $lead = Lead::find($id);
            if($lead->created_by == \Auth::user()->creatorId())
            {
                $sources = Source::where('created_by', '=', \Auth::user()->creatorId())->get();

                $selected = $lead->sources();
                if($selected)
                {
                    $selected = $selected->pluck('name', 'id')->toArray();
                }

                return view('leads.sources', compact('lead', 'sources', 'selected'));
            }
            else
            {
                return response()->json(['error' => __('Permission Denied.')], 401);
            }
        }
        else
        {
            return response()->json(['error' => __('Permission Denied.')], 401);
        }
    }

    public function sourceUpdate($id, Request $request)
    {
        if(\Auth::user()->can('edit lead'))
        {
            $validator = \Validator::make($request->all(), [
                    'sources' => 'required',
                ]
            );

            if($validator->fails())
            {
                $messages = $validator->getMessageBag();
                return redirect()->back()->with('error', $messages->first());
            }

            $usr        = \Auth::user();
            $lead       = Lead::find($id);
            $lead_users = $lead->users->pluck('id')->toArray();

            if($lead->created_by == \Auth::user()->creatorId())
            {
                if(!empty($request->sources) && count($request->sources) > 0)
                {
                    $lead->sources = implode(',', $request->sources);
                }
                else
                {
                    $lead->sources = "";
                }

                $lead->save();

                LeadActivityLog::create(
                    [
                        'user_id' => $usr->id,
                        'lead_id' => $lead->id,
                        'log_type' => 'Update Sources',
                        'remark' => json_encode(['title' => 'Update Sources']),
                    ]
                );

                $leadArr = [
                    'lead_id' => $lead->id,
                    'name' => $lead->name,
                    'updated_by' => $usr->id,
                ];

                return redirect()->back()->with('success', __('Sources successfully updated!'))->with('status', 'sources');
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'))->with('status', 'sources');
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'))->with('status', 'sources');
        }
    }

    public function sourceDestroy($id, $source_id)
    {
        if(\Auth::user()->can('edit lead'))
        {
            $lead = Lead::find($id);
            if($lead->created_by == \Auth::user()->creatorId())
            {
                $sources = explode(',', $lead->sources);
                foreach($sources as $key => $source)
                {
                    if($source_id == $source)
                    {
                        unset($sources[$key]);
                    }
                }
                $lead->sources = implode(',', $sources);
                $lead->save();

                return redirect()->back()->with('success', __('Sources successfully deleted!'))->with('status', 'sources');
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'))->with('status', 'sources');
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'))->with('status', 'sources');
        }
    }

    public function discussionCreate($id)
    {
        $lead = Lead::find($id);
        if($lead->created_by == \Auth::user()->creatorId())
        {
            return view('leads.discussions', compact('lead'));
        }
        else
        {
            return response()->json(['error' => __('Permission Denied.')], 401);
        }
    }

    public function discussionStore($id, Request $request)
    {
        $usr        = \Auth::user();
        $lead       = Lead::find($id);
        $lead_users = $lead->users->pluck('id')->toArray();

        if($lead->created_by == $usr->creatorId())
        {
            $discussion             = new LeadDiscussion();
            $discussion->comment    = $request->comment;
            $discussion->lead_id    = $lead->id;
            $discussion->created_by = $usr->id;
            $discussion->save();

            $leadArr = [
                'lead_id' => $lead->id,
                'name' => $lead->name,
                'updated_by' => $usr->id,
            ];

            return redirect()->back()->with('success', __('Message successfully added!'))->with('status', 'discussion');
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'))->with('status', 'discussion');
        }
    }

    public function order(Request $request)
    {
        if(\Auth::user()->can('move lead'))
        {
            $usr        = \Auth::user();
            $post       = $request->all();
            $lead       = $this->lead($post['lead_id']);
            $lead_users = $lead->users->pluck('email', 'id')->toArray();

            if($lead->stage_id != $post['stage_id'])
            {
                $oldStageId = $lead->stage_id;
                $newStage = LeadStage::find($post['stage_id']);

                LeadActivityLog::create(
                    [
                        'user_id' => \Auth::user()->id,
                        'lead_id' => $lead->id,
                        'log_type' => 'Move',
                        'remark' => json_encode(
                            [
                                'title' => $lead->name,
                                'old_status' => $lead->stage->name,
                                'new_status' => $newStage->name,
                            ]
                        ),
                    ]
                );

                $leadArr = [
                    'lead_id' => $lead->id,
                    'name' => $lead->name,
                    'updated_by' => $usr->id,
                    'old_status' => $lead->stage->name,
                    'new_status' => $newStage->name,
                ];

                $lArr = [
                    'lead_name' => $lead->name,
                    'lead_email' => $lead->email,
                    'lead_pipeline' => $lead->pipeline->name,
                    'lead_stage' => $lead->stage->name,
                    'lead_old_stage' => $lead->stage->name,
                    'lead_new_stage' => $newStage->name,
                ];

                // Send Email
                Utility::sendEmailTemplate('Move Lead', $lead_users, $lArr);

                // Send webhook for stage change
                try {
                    $webhookDispatcher = new CrmWebhookDispatcher();
                    $webhookDispatcher->dispatchLeadStageChanged($lead, $oldStageId, $post['stage_id']);
                } catch (\Exception $e) {
                    \Log::error('Webhook dispatch failed for lead stage change', ['error' => $e->getMessage()]);
                }
            }

            foreach($post['order'] as $key => $item)
            {
                $lead           = $this->lead($item);
                $lead->order    = $key;
                $lead->stage_id = $post['stage_id'];
                $lead->save();
            }
            return response()->json(['status' => 'success','message' => __('Lead successfully moved.')], 200);
        }
        else
        {
            return response()->json(['status' => 'error','message' => __('Permission Denied.')], 401);
        }
    }

    private static $leadData = NULL;

    public function lead($item)
    {
        if(self::$leadData == null)
        {
            $lead = Lead::find($item);

            self::$leadData = $lead;
        }
        return self::$leadData;
    }

    public function showConvertToDeal($id)
    {

        $lead         = Lead::findOrFail($id);
        $exist_client = User::where('type', '=', 'client')->where('email', '=', $lead->email)->where('created_by', '=', \Auth::user()->creatorId())->first();
        $clients      = User::where('type', '=', 'client')->where('created_by', '=', \Auth::user()->creatorId())->get();

        return view('leads.convert', compact('lead', 'exist_client', 'clients'));
    }

    public function convertToDeal($id, Request $request)
    {
        $lead = Lead::findOrFail($id);
        $usr  = \Auth::user();

        if($request->client_check == 'exist')
        {
            $validator = \Validator::make(
                $request->all(), [
                                   'clients' => 'required',
                               ]
            );

            if($validator->fails())
            {
                $messages = $validator->getMessageBag();

                return redirect()->back()->with('error', $messages->first());
            }

            $client = User::where('type', '=', 'client')->where('email', '=', $request->clients)->where('created_by', '=', $usr->creatorId())->first();

            if(empty($client))
            {
                return redirect()->back()->with('error', 'Client is not available now.');
            }
        }
        else
        {
            $validator = \Validator::make(
                $request->all(), [
                                   'client_name' => 'required',
                                   'client_email' => 'required|email|unique:users,email',
                                   'client_password' => 'required',
                               ]
            );

            if($validator->fails())
            {
                $messages = $validator->getMessageBag();

                return redirect()->back()->with('error', $messages->first());
            }

            $role   = Role::findByName('client');
            $client = User::create(
                [
                    'name' => $request->client_name,
                    'email' => $request->client_email,
                    'password' => \Hash::make($request->client_password),
                    'type' => 'client',
                    'lang' => 'en',
                    'created_by' => $usr->creatorId(),
                ]
            );
            $client->assignRole($role);

            $cArr = [
                'email' => $request->client_email,
                'password' => $request->client_password,
            ];

            // Send Email to client if they are new created.
            Utility::sendEmailTemplate('New User', [$client->id => $client->email], $cArr);
        }

        // Create Deal
        $stage = Stage::where('pipeline_id', '=', $lead->pipeline_id)->first();
        if(empty($stage))
        {
            return redirect()->back()->with('error', __('Please Create Stage for This Pipeline.'));
        }

        $deal              = new Deal();
        $deal->name        = $request->name;
        $deal->price       = empty($request->price) ? 0 : $request->price;
        $deal->pipeline_id = $lead->pipeline_id;
        $deal->stage_id    = $stage->id;
        if (!empty($request->is_transfer))
        {
            $deal->sources     = in_array('sources', $request->is_transfer) ? $lead->sources : '';
            $deal->products    = in_array('products', $request->is_transfer) ? $lead->products : '';
            $deal->notes       = in_array('notes', $request->is_transfer) ? $lead->notes : '';
        }
        else
        {
            $deal->sources     = '';
            $deal->products    = '';
            $deal->notes       = '';
        }

        $deal->labels      = $lead->labels;
        $deal->status      = 'Active';
        $deal->created_by  = $lead->created_by;
        $deal->save();
        // end create deal

        // Make entry in ClientDeal Table
        ClientDeal::create(
            [
                'deal_id' => $deal->id,
                'client_id' => $client->id,
            ]
        );
        // end

        $dealArr = [
            'deal_id' => $deal->id,
            'name' => $deal->name,
            'updated_by' => $usr->id,
        ];
        // Send Notification

        // Send Mail
        $pipeline = Pipeline::find($lead->pipeline_id);
        $dArr     = [
            'deal_name' => $deal->name,
            'deal_pipeline' => $pipeline->name,
            'deal_stage' => $stage->name,
            'deal_status' => $deal->status,
            'deal_price' => $usr->priceFormat($deal->price),
        ];
        Utility::sendEmailTemplate('Assign Deal', [$client->id => $client->email], $dArr);

        // Make Entry in UserDeal Table
        $leadUsers = UserLead::where('lead_id', '=', $lead->id)->get();
        foreach($leadUsers as $leadUser)
        {
            UserDeal::create(
                [
                    'user_id' => $leadUser->user_id,
                    'deal_id' => $deal->id,
                ]
            );
        }
        // end

        //Transfer Lead Discussion to Deal
        if (!empty($request->is_transfer))
        {
            if(in_array('discussion', $request->is_transfer))
            {
                $discussions = LeadDiscussion::where('lead_id', '=', $lead->id)->where('created_by', '=', $usr->creatorId())->get();
                if(!empty($discussions))
                {
                    foreach($discussions as $discussion)
                    {
                        DealDiscussion::create(
                            [
                                'deal_id' => $deal->id,
                                'comment' => $discussion->comment,
                                'created_by' => $discussion->created_by,
                            ]
                        );
                    }
                }
            }
            // end Transfer Discussion

            // Transfer Lead Files to Deal
            if(in_array('files', $request->is_transfer))
            {
                $files = LeadFile::where('lead_id', '=', $lead->id)->get();
                if(!empty($files))
                {
                    foreach($files as $file)
                    {
                        $location     = base_path() . '/storage/lead_files/' . $file->file_path;
                        $new_location = base_path() . '/storage/deal_files/' . $file->file_path;
                        $copied       = copy($location, $new_location);

                        if($copied)
                        {
                            DealFile::create(
                                [
                                    'deal_id' => $deal->id,
                                    'file_name' => $file->file_name,
                                    'file_path' => $file->file_path,
                                ]
                            );
                        }
                    }
                }
            }
            // end Transfer Files

            // Transfer Lead Calls to Deal
            if(in_array('calls', $request->is_transfer))
            {
                $calls = LeadCall::where('lead_id', '=', $lead->id)->get();
                if(!empty($calls))
                {
                    foreach($calls as $call)
                    {
                        DealCall::create(
                            [
                                'deal_id' => $deal->id,
                                'subject' => $call->subject,
                                'call_type' => $call->call_type,
                                'duration' => $call->duration,
                                'user_id' => $call->user_id,
                                'description' => $call->description,
                                'call_result' => $call->call_result,
                            ]
                        );
                    }
                }
            }
            //end

            // Transfer Lead Emails to Deal
            if(in_array('emails', $request->is_transfer))
            {
                $emails = LeadEmail::where('lead_id', '=', $lead->id)->get();
                if(!empty($emails))
                {
                    foreach($emails as $email)
                    {
                        DealEmail::create(
                            [
                                'deal_id' => $deal->id,
                                'to' => $email->to,
                                'subject' => $email->subject,
                                'description' => $email->description,
                            ]
                        );
                    }
                }
            }
        }
        // Update is_converted field as deal_id
        $lead->is_converted = $deal->id;
        $lead->save();

        //For Notification
        $setting  = Utility::settings(\Auth::user()->creatorId());
        $leadUsers = Lead::where('id', '=', $lead->id)->first();
        $leadUserArr = [
            'lead_user_name' => $leadUsers->name,
            'lead_name' => $lead->name,
            'lead_email' => $lead->email,
        ];
        //Slack Notification
        if(isset($setting['leadtodeal_notification']) && $setting['leadtodeal_notification'] ==1)
        {
            Utility::send_slack_msg('lead_to_deal_conversion', $leadUserArr);
        }
        //Telegram Notification
        if(isset($setting['telegram_leadtodeal_notification']) && $setting['telegram_leadtodeal_notification'] ==1)
        {
            Utility::send_telegram_msg('lead_to_deal_conversion', $leadUserArr);
        }

        // Send webhook to integrated modules
        $webhookDispatcher = new CrmWebhookDispatcher();
        $webhookDispatcher->dispatchLeadConvertedToDeal($lead, $deal);

        return redirect()->back()->with('success', __('Lead successfully converted'));
    }

    // Lead Calls
    public function callCreate($id)
    {
        if(\Auth::user()->can('create lead call'))
        {
            $lead = Lead::find($id);
            if($lead->created_by == \Auth::user()->creatorId())
            {
                $users = UserLead::where('lead_id', '=', $lead->id)->get();

                return view('leads.calls', compact('lead', 'users'));
            }
            else
            {
                return response()->json(
                    [
                        'is_success' => false,
                        'error' => __('Permission Denied.'),
                    ], 401
                );
            }
        }
        else
        {
            return response()->json(
                [
                    'is_success' => false,
                    'error' => __('Permission Denied.'),
                ], 401
            );
        }
    }

    public function callStore($id, Request $request)
    {
        if(\Auth::user()->can('create lead call'))
        {
            $usr  = \Auth::user();
            $lead = Lead::find($id);
            if($lead->created_by == \Auth::user()->creatorId())
            {
                $validator = \Validator::make(
                    $request->all(), [
                                       'subject' => 'required',
                                       'call_type' => 'required',
                                       'user_id' => 'required',
                                   ]
                );

                if($validator->fails())
                {
                    $messages = $validator->getMessageBag();

                    return redirect()->back()->with('error', $messages->first());
                }

                $leadCall = LeadCall::create(
                    [
                        'lead_id' => $lead->id,
                        'subject' => $request->subject,
                        'call_type' => $request->call_type,
                        'duration' => $request->duration,
                        'user_id' => $request->user_id,
                        'description' => $request->description,
                        'call_result' => $request->call_result,
                    ]
                );

                LeadActivityLog::create(
                    [
                        'user_id' => $usr->id,
                        'lead_id' => $lead->id,
                        'log_type' => 'create lead call',
                        'remark' => json_encode(['title' => 'Create new Lead Call']),
                    ]
                );

                $leadArr = [
                    'lead_id' => $lead->id,
                    'name' => $lead->name,
                    'updated_by' => $usr->id,
                ];

                return redirect()->back()->with('success', __('Call successfully created!'))->with('status', 'calls');
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'))->with('status', 'calls');
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'))->with('status', 'calls');
        }
    }

    public function callEdit($id, $call_id)
    {
        if(\Auth::user()->can('edit lead call'))
        {
            $lead = Lead::find($id);
            if($lead->created_by == \Auth::user()->creatorId())
            {
                $call  = LeadCall::find($call_id);
                $users = UserLead::where('lead_id', '=', $lead->id)->get();

                return view('leads.calls', compact('call', 'lead', 'users'));
            }
            else
            {
                return response()->json(
                    [
                        'is_success' => false,
                        'error' => __('Permission Denied.'),
                    ], 401
                );
            }
        }
        else
        {
            return response()->json(
                [
                    'is_success' => false,
                    'error' => __('Permission Denied.'),
                ], 401
            );

        }
    }

    public function callUpdate($id, $call_id, Request $request)
    {
        if(\Auth::user()->can('edit lead call'))
        {
            $lead = Lead::find($id);
            if($lead->created_by == \Auth::user()->creatorId())
            {
                $validator = \Validator::make(
                    $request->all(), [
                                       'subject' => 'required',
                                       'call_type' => 'required',
                                       'user_id' => 'required',
                                   ]
                );

                if($validator->fails())
                {
                    $messages = $validator->getMessageBag();

                    return redirect()->back()->with('error', $messages->first());
                }

                $call = LeadCall::find($call_id);

                $call->update(
                    [
                        'subject' => $request->subject,
                        'call_type' => $request->call_type,
                        'duration' => $request->duration,
                        'user_id' => $request->user_id,
                        'description' => $request->description,
                        'call_result' => $request->call_result,
                    ]
                );

                return redirect()->back()->with('success', __('Call successfully updated!'))->with('status', 'calls');
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'))->with('status', 'calls');
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'))->with('status', 'tasks');
        }
    }

    public function callDestroy($id, $call_id)
    {
        if(\Auth::user()->can('delete lead call'))
        {
            $lead = Lead::find($id);
            if($lead->created_by == \Auth::user()->creatorId())
            {
                $task = LeadCall::find($call_id);
                $task->delete();

                return redirect()->back()->with('success', __('Call successfully deleted!'))->with('status', 'calls');
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'))->with('status', 'calls');
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'))->with('status', 'calls');
        }
    }

    // Lead email
    public function emailCreate($id)
    {
        if(\Auth::user()->can('create lead email'))
        {
            $lead = Lead::find($id);
            if($lead->created_by == \Auth::user()->creatorId())
            {
                return view('leads.emails', compact('lead'));
            }
            else
            {
                return response()->json(
                    [
                        'is_success' => false,
                        'error' => __('Permission Denied.'),
                    ], 401
                );
            }
        }
        else
        {
            return response()->json(
                [
                    'is_success' => false,
                    'error' => __('Permission Denied.'),
                ], 401
            );
        }
    }

    public function emailStore($id, Request $request)
    {

        if(\Auth::user()->can('create lead email'))
        {
            $lead = Lead::find($id);

            if($lead->created_by == \Auth::user()->creatorId())
            {
                $settings  = Utility::settings();
                $validator = \Validator::make(
                    $request->all(), [
                                       'to' => 'required|email',
                                       'subject' => 'required',
                                       'description' => 'required',
                                   ]
                );

                if($validator->fails())
                {
                    $messages = $validator->getMessageBag();

                    return redirect()->back()->with('error', $messages->first());
                }

                $leadEmail = LeadEmail::create(
                    [
                        'lead_id' => $lead->id,
                        'to' => $request->to,
                        'subject' => $request->subject,
                        'description' => $request->description,
                    ]
                );

                $leadEmail =
                    [
                        'lead_name' => $lead->name,
                        'to' => $request->to,
                        'subject' => $request->subject,
                        'description' => $request->description,
                    ];


                try
                {
                    Mail::to($request->to)->send(new SendLeadEmail($leadEmail, $settings));
                }
                catch(\Exception $e)
                {

                    $smtp_error = __('E-Mail has been not sent due to SMTP configuration');
                }
//

                LeadActivityLog::create(
                    [
                        'user_id' => \Auth::user()->id,
                        'lead_id' => $lead->id,
                        'log_type' => 'create lead email',
                        'remark' => json_encode(['title' => 'Create new Deal Email']),
                    ]
                );

                return redirect()->back()->with('success', __('Email successfully created!') . ((isset($smtp_error)) ? '<br> <span class="text-danger">' . $smtp_error . '</span>' : ''))->with('status', 'emails');
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'))->with('status', 'emails');
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'))->with('status', 'emails');
        }
    }

    public function export()
    {
        $name = 'Lead_' . date('Y-m-d i:h:s');
        $data = Excel::download(new LeadExport(), $name . '.xlsx'); ob_end_clean();

        return $data;
    }

    public function importFile()
    {
        return view('leads.import');
    }

    public function fileImport(Request $request)
    {
        session_start();

        $error = '';

        $html = '';

        if ($request->hasFile('file') && $request->file->getClientOriginalName() != '') {
            $file_array = explode(".", $request->file->getClientOriginalName());

            $extension = end($file_array);
            if ($extension == 'csv') {
                $file_data = fopen($request->file->getRealPath(), 'r');

                $file_header = fgetcsv($file_data);
                $html .= '<table class="table table-bordered"><tr>';

                for ($count = 0; $count < count($file_header); $count++) {
                    $html .= '
                                <th>
                                    <select name="set_column_data" class="form-control set_column_data" data-column_number="' . $count . '">
                                        <option value="">Set Count Data</option>
                                        <option value="subject">Subject</option>
                                        <option value="name">Name</option>
                                        <option value="email">Email</option>
                                        <option value="phone">Phone No</option>
                                    </select>
                                </th>
                                ';
                }
                $html .= '
                                <th>
                                        <select name="set_column_data" class="form-control set_column_data user-name" data-column_number="' . $count + 1 . '">
                                            <option value="user">User</option>
                                        </select>
                                </th>
                                ';
                $html .= '</tr>';
                $limit = 0;
                $temp_data = [];
                while (($row = fgetcsv($file_data)) !== false) {
                    $limit++;

                    $html .= '<tr>';

                    for ($count = 0; $count < count($row); $count++) {
                        $html .= '<td>' . $row[$count] . '</td>';
                    }

                    $html .= '<td>
                                    <select name="user" class="form-control user-name-value">;';
                        if (\Auth::user()->type == "company") {
                            $users = User::where('created_by', '=', \Auth::user()->creatorId())->where('type', '!=', 'client')->get()->pluck('name', 'id');
                        } else {
                            $users = User::where('id', '=', \Auth::user()->id)->where('type', '!=', 'client')->get()->pluck('name', 'id');
                        }
                        foreach ($users as $key => $user) {
                            $html .= ' <option value="' . $key . '">' . $user . '</option>';
                        }
                        $html .= '  </select>
                                </td>';


                    $html .= '</tr>';

                    $temp_data[] = $row;

                }
                $_SESSION['file_data'] = $temp_data;
            } else {
                $error = 'Only <b>.csv</b> file allowed';
            }
        } else {

            $error = 'Please Select CSV File';
        }
        $output = array(
            'error' => $error,
            'output' => $html,
        );

        return json_encode($output);


    }

    public function fileImportModal()
    {
        return view('leads.import_modal');
    }

    public function leadImportdata(Request $request)
    {
        $creatorId          = \Auth::user()->creatorId();
        session_start();
        $html = '<h3 class="text-danger text-center">Below data is not inserted</h3></br>';
        $flag = 0;
        $html .= '<table class="table table-bordered"><tr>';
        try {
            $file_data = $_SESSION['file_data'];

            // foreach ($file_data as $validationKey => $value) {
            //     $validator = \Validator::make([
            //         'subject' => $value[$request->subject] ?? null,
            //         'name'    => $value[$request->name] ?? null,
            //         'email'   => $value[$request->email] ?? null,
            //         'phone' => $value[$request->phone] ?? null,
            //     ], [
            //         'subject' => 'required|string|max:255',
            //         'name'    => 'required|string|max:255',
            //         'email'   => 'required|email|max:255',
            //         'phone' => 'required|regex:/^\+\d{1,3}\d{9,13}$/'
            //     ]);

            //     if ($validator->fails()) {
            //         return response()->json([
            //             'success' => false,
            //             'message' => $validator->errors()->first(),
            //         ]);
            //     }
            // }

            unset($_SESSION['file_data']);
        } catch (\Throwable $th) {
            $html = '<h3 class="text-danger text-center">Oops, Session Time Out!</h3></br>';
            return response()->json([
                'html' => true,
                'response' => $html,
            ]);
        }

        $user = \Auth::user();
        if ($user->default_pipeline) {
            $pipeline = Pipeline::where('created_by', '=', $creatorId)->where('id', '=', $user->default_pipeline)->first();
            if (!$pipeline) {
                $pipeline = Pipeline::where('created_by', '=', $creatorId)->first();
            }
        } else {
            $pipeline = Pipeline::where('created_by', '=', $creatorId)->first();
        }

        if (!empty($pipeline)) {
            $stage = LeadStage::where('pipeline_id', '=', $pipeline->id)->first();
            if (empty($stage)) {
                return response()->json([
                    'success' => false,
                    'message' => __('Please create stage for this pipeline.'),
                ]);
            }
        } else {
            return response()->json([
                'success' => false,
                'message' => __('Please create pipeline.'),
            ]);
        }

        foreach ($file_data as $key => $row) {
            try {
                $users = User::find($request->user[$key]);
                if (empty($users)) {
                    $users = User::where('created_by', \Auth::user()->id)->first();
                }

                $lead = Lead::create([
                    'subject' => $row[$request->subject],
                    'name' => $row[$request->name],
                    'user_id' => $users->id,
                    'email' => $row[$request->email],
                    'phone' => $row[$request->phone],
                    'pipeline_id' => $pipeline->id,
                    'stage_id' => $stage->id,
                    'created_by' => $creatorId,
                ]);
                UserLead::create([
                    'user_id' => $creatorId,
                    'lead_id' => $lead->id,
                ]);
            } catch (\Exception $e) {
                $flag = 1;
                $html .= '<tr>';

                $html .= '<td>' . (isset($row[$request->subject]) ? $row[$request->subject] : '-') . '</td>';
                $html .= '<td>' . (isset($row[$request->name]) ? $row[$request->name] : '-') . '</td>';
                $html .= '<td>' . (isset($row[$request->email]) ? $row[$request->email] : '-') . '</td>';
                $html .= '<td>' . (isset($row[$request->phone]) ? $row[$request->phone] : '-') . '</td>';

                $html .= '</tr>';
            }
        }
        $html .= '
                </table>
                <br />
                ';
        if ($flag == 1) {
            return response()->json([
                'html' => true,
                'response' => $html,
            ]);
        } else {
            return response()->json([
                'html' => false,
                'response' => __('Data has been imported.'),
            ]);
        }
    }

    public function taskCreate($id)
    {
        $lead = Lead::find($id);
        $priorities = LeadTask::$priorities;
        $status = LeadTask::$status;
        $users = User::where('created_by', \Auth::user()->creatorId())->get();
        return view('leads.tasks.create', compact('lead', 'priorities', 'status', 'users'));
    }

    public function taskStore($id, Request $request)
    {
        if(\Auth::user()->can('create lead task'))
        {
            $lead       = Lead::find($id);
            $validator = \Validator::make(
                $request->all(), [
                                   'name' => 'required',
                                   'date' => 'required',
                                   'time' => 'required',
                                   'priority' => 'required',
                                   'status' => 'required',
                                   'assign_to' => 'required|exists:users,id',
                               ]
            );
            if($validator->fails())
            {
                $messages = $validator->getMessageBag();
                return redirect()->back()->with('error', $messages->first());
            }
            $task = new LeadTask();
            $task->lead_id  = $lead->id;
            $task->name     = $request->name;
            $task->date     = $request->date;
            $task->time     = date('H:i:s', strtotime($request->time));
            $task->priority = $request->priority;
            $task->status   = $request->status;
            $task->assign_to = $request->assign_to;
            $task->save();

            // Send webhook for task creation
            $webhookDispatcher = new CrmWebhookDispatcher();
            $webhookDispatcher->dispatchLeadTaskCreated($task);

            return redirect()->back()->with('success', __('Task successfully created!'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    public function taskShow($id, $task_id)
    {
        if(\Auth::user()->can('view lead task'))
        {
            $lead = Lead::find($id);
            $task = LeadTask::find($task_id);

            return view('leads.tasks.show', compact('task', 'lead'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    public function taskEdit($id, $task_id)
    {
        if(\Auth::user()->can('edit lead task'))
        {
            $lead = Lead::find($id);
            $task = LeadTask::find($task_id);
            $priorities = LeadTask::$priorities;
            $status = LeadTask::$status;
            $users = User::where('created_by', \Auth::user()->creatorId())->get();
            return view('leads.tasks.edit', compact('lead', 'task', 'priorities', 'status', 'users'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    public function taskUpdate($id, $task_id, Request $request)
    {
        if(\Auth::user()->can('edit lead task'))
        {
            $lead       = Lead::find($id);
            $validator = \Validator::make(
                $request->all(), [
                                   'name' => 'required',
                                   'date' => 'required',
                                   'time' => 'required',
                                   'priority' => 'required',
                                   'status' => 'required',
                                   'assign_to' => 'required|exists:users,id',
                               ]
            );
            if($validator->fails())
            {
                $messages = $validator->getMessageBag();
                return redirect()->back()->with('error', $messages->first());
            }
            $task = LeadTask::find($task_id);
            $task->name     = $request->name;
            $task->date     = $request->date;
            $task->time     = date('H:i:s', strtotime($request->time));
            $task->priority = $request->priority;
            $task->status   = $request->status;
            $task->assign_to = $request->assign_to;
            $task->save();
            return redirect()->back()->with('success', __('Follow Ups successfully updated!'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    public function taskDestroy($id, $task_id)
    {
        if(\Auth::user()->can('delete lead task'))
        {
            $lead = Lead::find($id);
            $task = LeadTask::find($task_id);
            $task->delete();

            return redirect()->back()->with('success', __('Follow Ups successfully deleted!'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    public function taskUpdateStatus($id, $task_id, Request $request)
    {
        if(\Auth::user()->can('edit lead task'))
        {
            $lead       = Lead::find($id);
            $validator = \Validator::make(
                $request->all(), [
                                   'status' => 'required',
                               ]
            );

            if($validator->fails())
            {
                $messages = $validator->getMessageBag();

                return redirect()->back()->with('error', $messages->first());
            }

            $task = LeadTask::find($task_id);
            $oldStatus = $task->status;
            $task->status = $request->status;
            $task->save();

            // Send webhook if task was completed
            if ($oldStatus != 1 && $request->status == 1) {
                $webhookDispatcher = new CrmWebhookDispatcher();
                $webhookDispatcher->dispatchLeadTaskCompleted($task);
            }

            return redirect()->back()->with('success', __('Task status successfully updated!'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }
    public function storeComment(Request $request, $leadId)
    {
        $request->validate([
            'comment' => 'required|string|max:1000',
        ]);
    
        $lead = Lead::findOrFail($leadId);
        
        // Create comment
        $comment = new \App\Models\LeadComment();
        $comment->lead_id = $lead->id;
        $comment->user_id = \Auth::id();
        $comment->comment = $request->comment;
        $comment->created_by = \Auth::user()->creatorId();
        $comment->save();
    
        return redirect()->back()->with('success', __('Comment added successfully.'));
    }
    
    public function deleteComment($commentId)
    {
        $comment = \App\Models\LeadComment::findOrFail($commentId);
        
        // Check if user can delete this comment
        if ($comment->user_id == \Auth::id() || \Auth::user()->can('delete lead comment')) {
            $comment->delete();
            return response()->json(['success' => true]);
        }
        
        return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
    }

    public function mentionUsers()
{
    $users = \App\Models\User::where('created_by', \Auth::user()->creatorId())
                ->whereNotIn('type', ['client', 'company'])
                ->select('id', 'name')
                ->get();

    return response()->json($users);
}

public function reactToComment(Request $request, $commentId)
{
    $request->validate([
        'reaction' => 'required|in:like,love,laugh,angry,sad,wow'
    ]);

    $comment = \App\Models\LeadComment::findOrFail($commentId);
    
    // Check if user already reacted
    $existingReaction = \App\Models\LeadCommentReaction::where('comment_id', $commentId)
        ->where('user_id', \Auth::id())
        ->first();

    if ($existingReaction) {
        // Update existing reaction
        $existingReaction->reaction = $request->reaction;
        $existingReaction->save();
    } else {
        // Create new reaction
        \App\Models\LeadCommentReaction::create([
            'comment_id' => $commentId,
            'user_id' => \Auth::id(),
            'reaction' => $request->reaction
        ]);
    }

    return response()->json([
        'success' => true,
        'reactions' => $comment->getReactionCounts(),
        'userReaction' => $request->reaction
    ]);
}

public function unreactToComment($commentId)
{
    \App\Models\LeadCommentReaction::where('comment_id', $commentId)
        ->where('user_id', \Auth::id())
        ->delete();

    $comment = \App\Models\LeadComment::findOrFail($commentId);

    return response()->json([
        'success' => true,
        'reactions' => $comment->getReactionCounts(),
        'userReaction' => null
    ]);
}



    public function getSalesForecastData()
    {
        if(\Auth::user()->can('view lead'))
        {
            $time_periods = [
                'this_week' => __('This Week'),
                'this_month' => __('This Month'),
                'next_quarter' => __('Next Quarter'),
                'this_year' => __('This Year')
            ];

            $forecast_data = [];
            $user_id = \Auth::user()->creatorId();
            
            // Get all deals with their stages, pipelines, and sources
            $deals = \App\Models\Deal::where('created_by', '=', $user_id)
                ->with(['stage', 'pipeline', 'sources', 'products'])
                ->get();

            // Get won deals count for conversion rate calculation
            $won_stage = \App\Models\Stage::where('created_by', $user_id)
                ->where('pipeline_id', '!=', 0)
                ->where('name', 'Won')
                ->first();

            $won_deals = $won_stage ? 
                \App\Models\Deal::where('stage_id', $won_stage->id)
                    ->where('created_by', $user_id)
                    ->get()
                    ->keyBy('id') : [];

            // Group by time period
            foreach ($time_periods as $period => $label) {
                $forecast_data[$period] = [
                    'label' => $label,
                    'total_value' => 0,
                    'weighted_value' => 0,
                    'deal_count' => 0,
                    'by_source' => [],
                    'by_product' => []
                ];
            }

            // Calculate forecast values
            foreach ($deals as $deal) {
                $probability = $deal->probability ? $deal->probability / 100 : 0.5;
                $close_date = $deal->created_at;
                $period = $this->getTimePeriod($close_date);

                if (isset($forecast_data[$period])) {
                    $is_won = isset($won_deals[$deal->id]);
                    
                    // Update period totals
                    $forecast_data[$period]['total_value'] += $deal->price;
                    $forecast_data[$period]['weighted_value'] += $deal->price * $probability;
                    $forecast_data[$period]['deal_count']++;

                    // Process sources
                    if ($deal->sources->isNotEmpty()) {
                        foreach ($deal->sources as $source) {
                            $sourceName = $source->name;
                            
                            if (!isset($forecast_data[$period]['by_source'][$sourceName])) {
                                $forecast_data[$period]['by_source'][$sourceName] = [
                                    'total' => 0,
                                    'weighted' => 0,
                                    'count' => 0,
                                    'won_deals' => 0
                                ];
                            }
                            
                            $forecast_data[$period]['by_source'][$sourceName]['total'] += $deal->price;
                            $forecast_data[$period]['by_source'][$sourceName]['weighted'] += $deal->price * $probability;
                            $forecast_data[$period]['by_source'][$sourceName]['count']++;
                            
                            if ($is_won) {
                                $forecast_data[$period]['by_source'][$sourceName]['won_deals']++;
                            }
                        }
                    }

                    // Process products
                    if ($deal->products->isNotEmpty()) {
                        foreach ($deal->products as $product) {
                            $productName = $product->name;
                            $sourceName = $deal->sources->isNotEmpty() ? $deal->sources->first()->name : 'Unknown';
                            
                            if (!isset($forecast_data[$period]['by_product'][$productName])) {
                                $forecast_data[$period]['by_product'][$productName] = [
                                    'price' => $product->price,
                                    'total' => 0,
                                    'weighted' => 0,
                                    'count' => 0,
                                    'source' => $sourceName
                                ];
                            }
                            
                            $forecast_data[$period]['by_product'][$productName]['total'] += $product->price * $product->quantity;
                            $forecast_data[$period]['by_product'][$productName]['weighted'] += ($product->price * $product->quantity) * $probability;
                            $forecast_data[$period]['by_product'][$productName]['count'] += $product->quantity;
                        }
                    }
                }
            }

            return response()->json([
                'status' => 'success',
                'forecast_data' => $forecast_data,
                'currency' => '₹' // TODO: Get from settings
            ]);
        }
        else
        {
            return response()->json(['status' => 'error', 'message' => __('Permission Denied.')], 401);
        }
    }

    public function bulkDelete(Request $request)
    {
        $request->validate([
            'lead_ids' => 'required|array',
            'lead_ids.*' => 'integer|exists:leads,id',
        ]);

        // Optionally, add authorization logic here

        \App\Models\Lead::whereIn('id', $request->lead_ids)
            ->update(['is_deleted' => 1]);

        return response()->json(['status' => 'success']);
    }

    /**
     * Get stages for a specific pipeline
     */
    public function getPipelineStages(Request $request)
    {
        $pipelineId = $request->input('pipeline_id');

        // Add debugging
        \Log::info('getPipelineStages called', [
            'pipeline_id' => $pipelineId,
            'user_id' => \Auth::id(),
            'creator_id' => \Auth::user()->creatorId(),
            'request_data' => $request->all()
        ]);

        if (!$pipelineId) {
            \Log::warning('Pipeline ID not provided in request');
            return response()->json([
                'success' => false,
                'message' => 'Pipeline ID is required'
            ], 400);
        }

        try {
            // Query lead_stages table directly
            $stages = \DB::table('lead_stages')
                ->where('pipeline_id', $pipelineId)
                ->where('created_by', \Auth::user()->creatorId())
                ->orderBy('order')
                ->select('id', 'name', 'order')
                ->get();

            \Log::info('Stages query result', [
                'pipeline_id' => $pipelineId,
                'stages_count' => $stages->count(),
                'stages' => $stages->toArray()
            ]);

            return response()->json([
                'success' => true,
                'stages' => $stages,
                'debug' => [
                    'pipeline_id' => $pipelineId,
                    'creator_id' => \Auth::user()->creatorId(),
                    'stages_count' => $stages->count()
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching pipeline stages', [
                'error' => $e->getMessage(),
                'pipeline_id' => $pipelineId,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error fetching stages: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update lead pipeline assignment
     */
    public function updatePipeline(Request $request, $id)
    {
        if (!\Auth::user()->can('edit lead')) {
            return response()->json([
                'success' => false,
                'message' => 'Permission Denied.'
            ], 403);
        }

        $lead = Lead::where('id', $id)
            ->where('created_by', \Auth::user()->creatorId())
            ->first();

        if (!$lead) {
            return response()->json([
                'success' => false,
                'message' => 'Contact not found'
            ], 404);
        }

        $validator = \Validator::make($request->all(), [
            'pipeline_id' => 'required|exists:pipelines,id',
            'stage_id' => 'required|exists:lead_stages,id',
            'notes' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
                'errors' => $validator->errors()
            ], 422);
        }

        // Store original values for change tracking
        $originalStageId = $lead->stage_id;
        $originalPipelineId = $lead->pipeline_id;

        \Log::info('Updating contact pipeline', [
            'contact_id' => $id,
            'contact_name' => $lead->name,
            'original_pipeline_id' => $originalPipelineId,
            'new_pipeline_id' => $request->pipeline_id,
            'original_stage_id' => $originalStageId,
            'new_stage_id' => $request->stage_id,
            'notes' => $request->notes
        ]);

        // Update pipeline and stage
        $lead->pipeline_id = $request->pipeline_id;
        $lead->stage_id = $request->stage_id;

        // Update notes if provided
        if ($request->filled('notes')) {
            $lead->notes = $request->notes;
        }

        $lead->save();

        // Get pipeline and stage names for logging
        $pipeline = \App\Models\Pipeline::find($request->pipeline_id);
        $stage = \App\Models\LeadStage::find($request->stage_id);

        // Log activity
        $activityLog = new \App\Models\LeadActivityLog();
        $activityLog->lead_id = $lead->id;
        $activityLog->log_type = 'Pipeline Updated';
        $activityLog->remark = "Contact moved to pipeline '{$pipeline->name}' and stage '{$stage->name}'";
        $activityLog->created_by = \Auth::user()->creatorId();
        $activityLog->save();

        \Log::info('Contact pipeline updated successfully', [
            'contact_id' => $id,
            'contact_name' => $lead->name,
            'pipeline_name' => $pipeline->name,
            'stage_name' => $stage->name
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Contact moved to pipeline successfully!',
            'contact' => [
                'id' => $lead->id,
                'name' => $lead->name,
                'pipeline_id' => $lead->pipeline_id,
                'stage_id' => $lead->stage_id,
                'pipeline_name' => $pipeline->name,
                'stage_name' => $stage->name
            ]
        ]);
    }

    /**
     * Filter leads based on criteria
     */
    public function filter(Request $request)
    {
        // Check if user is authenticated
        $user = \Auth::user();
        if (!$user) {
            return response()->json(['error' => __('User not authenticated.')], 401);
        }

        if (!$user->can('manage lead')) {
            return response()->json(['error' => __('Permission Denied.')], 403);
        }

        try {
            $assignedTo = $request->input('assigned_to', []);
            $tags = $request->input('tags', []);
            $createdOnRange = $request->input('created_on_range', '');
            $createdOnStart = $request->input('created_on_start', '');
            $createdOnEnd = $request->input('created_on_end', '');
            $updatedOnRange = $request->input('updated_on_range', '');
            $updatedOnStart = $request->input('updated_on_start', '');
            $updatedOnEnd = $request->input('updated_on_end', '');
            $sources = $request->input('sources', []);
            
            // Get creator ID safely
            $creatorId = ($user->type == 'company' || $user->type == 'super admin') 
                ? $user->id 
                : $user->created_by;

            // Start with base query - get all leads for the company, not just current user's leads
            $query = Lead::select('leads.*')
                ->where('leads.created_by', '=', $creatorId)
                ->where('leads.is_converted', '=', 0)
                ->where('leads.is_deleted', '=', 0);

            // Get current pipeline
            if ($user->default_pipeline) {
                $pipeline = Pipeline::where('created_by', '=', $creatorId)
                                  ->where('id', '=', $user->default_pipeline)
                                  ->first();
            } else {
                $pipeline = Pipeline::where('created_by', '=', $creatorId)->first();
            }

        if ($pipeline) {
            $query->where('leads.pipeline_id', '=', $pipeline->id);
        }

        // Apply assigned to filter
        if (!empty($assignedTo)) {
            if (in_array('none', $assignedTo)) {
                // Handle "Assigned to None" - leads with no user assignments
                $otherUsers = array_filter($assignedTo, function($val) { return $val !== 'none'; });
                
                if (!empty($otherUsers)) {
                    // Include leads assigned to specific users OR leads with no assignments
                    $query->where(function($q) use ($otherUsers) {
                        $q->whereExists(function($subQ) use ($otherUsers) {
                              $subQ->select('user_id')
                                   ->from('user_leads')
                                   ->whereColumn('user_leads.lead_id', 'leads.id')
                                   ->whereIn('user_leads.user_id', $otherUsers);
                          })
                          ->orWhereNotExists(function($subQ) {
                              $subQ->select('user_id')
                                   ->from('user_leads')
                                   ->whereColumn('user_leads.lead_id', 'leads.id');
                          });
                    });
                } else {
                    // Only show leads with no assignments
                    $query->whereNotExists(function($subQ) {
                        $subQ->select('user_id')
                             ->from('user_leads')
                             ->whereColumn('user_leads.lead_id', 'leads.id');
                    });
                }
            } else {
                // Filter by specific users only
                $query->whereExists(function($subQ) use ($assignedTo) {
                    $subQ->select('user_id')
                         ->from('user_leads')
                         ->whereColumn('user_leads.lead_id', 'leads.id')
                         ->whereIn('user_leads.user_id', $assignedTo);
                });
            }
        }

        // Apply tag filter
        if (!empty($tags)) {
            $query->whereExists(function($subQ) use ($tags) {
                $subQ->select('label_id')
                     ->from('lead_labels')
                     ->whereColumn('lead_labels.lead_id', 'leads.id')
                     ->whereIn('lead_labels.label_id', $tags);
            });
        }

        // Apply date filter
        if (!empty($createdOnRange)) {
            switch ($createdOnRange) {
                case 'today':
                    $query->whereDate('leads.created_at', today());
                    break;
                case 'yesterday':
                    $query->whereDate('leads.created_at', today()->subDay());
                    break;
                case 'this_week':
                    $query->whereBetween('leads.created_at', [now()->startOfWeek(), now()->endOfWeek()]);
                    break;
                case 'last_week':
                    $query->whereBetween('leads.created_at', [now()->subWeek()->startOfWeek(), now()->subWeek()->endOfWeek()]);
                    break;
                case 'last_7_days':
                    $query->whereDate('leads.created_at', '>=', today()->subDays(7));
                    break;
                case 'last_30_days':
                    $query->whereDate('leads.created_at', '>=', today()->subDays(30));
                    break;
                case 'this_month':
                    $query->whereBetween('leads.created_at', [now()->startOfMonth(), now()->endOfMonth()]);
                    break;
                case 'last_month':
                    $query->whereBetween('leads.created_at', [now()->subMonth()->startOfMonth(), now()->subMonth()->endOfMonth()]);
                    break;
                case 'this_year':
                    $query->whereBetween('leads.created_at', [now()->startOfYear(), now()->endOfYear()]);
                    break;
                case 'last_year':
                    $query->whereBetween('leads.created_at', [now()->subYear()->startOfYear(), now()->subYear()->endOfYear()]);
                    break;
                case 'custom':
                    if (!empty($createdOnStart) && !empty($createdOnEnd)) {
                        $query->whereDate('leads.created_at', '>=', $createdOnStart)
                              ->whereDate('leads.created_at', '<=', $createdOnEnd);
                    }
                    break;
            }
        }

        // Apply updated date filter
        if (!empty($updatedOnRange)) {
            switch ($updatedOnRange) {
                case 'today':
                    $query->whereDate('leads.updated_at', today());
                    break;
                case 'yesterday':
                    $query->whereDate('leads.updated_at', today()->subDay());
                    break;
                case 'this_week':
                    $query->whereBetween('leads.updated_at', [now()->startOfWeek(), now()->endOfWeek()]);
                    break;
                case 'last_week':
                    $query->whereBetween('leads.updated_at', [now()->subWeek()->startOfWeek(), now()->subWeek()->endOfWeek()]);
                    break;
                case 'last_7_days':
                    $query->whereDate('leads.updated_at', '>=', today()->subDays(7));
                    break;
                case 'last_30_days':
                    $query->whereDate('leads.updated_at', '>=', today()->subDays(30));
                    break;
                case 'this_month':
                    $query->whereBetween('leads.updated_at', [now()->startOfMonth(), now()->endOfMonth()]);
                    break;
                case 'last_month':
                    $query->whereBetween('leads.updated_at', [now()->subMonth()->startOfMonth(), now()->subMonth()->endOfMonth()]);
                    break;
                case 'this_year':
                    $query->whereBetween('leads.updated_at', [now()->startOfYear(), now()->endOfYear()]);
                    break;
                case 'last_year':
                    $query->whereBetween('leads.updated_at', [now()->subYear()->startOfYear(), now()->subYear()->endOfYear()]);
                    break;
                case 'custom':
                    if (!empty($updatedOnStart) && !empty($updatedOnEnd)) {
                        $query->whereDate('leads.updated_at', '>=', $updatedOnStart)
                              ->whereDate('leads.updated_at', '<=', $updatedOnEnd);
                    }
                    break;
            }
        }

        // Apply source filter
        if (!empty($sources)) {
            $query->where(function($subQ) use ($sources) {
                foreach ($sources as $sourceId) {
                    $subQ->orWhereRaw('FIND_IN_SET(?, leads.sources) > 0', [$sourceId]);
                }
            });
        }

        $leads = $query->orderBy('leads.order')->get();

            return response()->json([
                'success' => true,
                'leads' => $leads->map(function($lead) {
                    return [
                        'id' => $lead->id,
                        'name' => $lead->name,
                        'email' => $lead->email,
                        'phone' => $lead->phone,
                        'stage_id' => $lead->stage_id
                    ];
                })
            ]);

        } catch (\Exception $e) {
            \Log::error('Error filtering leads: ' . $e->getMessage(), [
                'user_id' => $user->id ?? 'unknown',
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to filter leads',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get users for filter dropdown
     */
    public function getFilterUsers(Request $request)
    {
        // Enhanced authentication check
        if (!\Auth::check()) {
            \Log::warning('Filter users request without authentication');
            return response()->json(['error' => __('User not authenticated.')], 401);
        }

        $currentUser = \Auth::user();
        if (!$currentUser) {
            \Log::warning('Auth check passed but user is null');
            return response()->json(['error' => __('User not authenticated.')], 401);
        }

        // Log user details for debugging
        \Log::info('Filter users request', [
            'user_id' => $currentUser->id,
            'user_type' => $currentUser->type,
            'user_created_by' => $currentUser->created_by ?? 'null'
        ]);

        if (!$currentUser->can('manage lead')) {
            return response()->json(['error' => __('Permission Denied.')], 403);
        }

        try {
            // Get creator ID safely with null checks
            if ($currentUser->type == 'company' || $currentUser->type == 'super admin') {
                $creatorId = $currentUser->id;
            } else {
                $creatorId = $currentUser->created_by;
                if (!$creatorId) {
                    \Log::error('User has no created_by value', ['user_id' => $currentUser->id]);
                    return response()->json(['error' => 'Invalid user data'], 400);
                }
            }

            \Log::info('Fetching users for filter', [
                'current_user_id' => $currentUser->id,
                'current_user_type' => $currentUser->type,
                'creator_id' => $creatorId
            ]);

            // Get all users from the company (including company owner)
            $users = User::where(function($query) use ($creatorId, $currentUser) {
                // Include users created by the company owner
                $query->where('created_by', $creatorId)
                      // Include the company owner themselves
                      ->orWhere('id', $creatorId);
            })
            ->where('type', '!=', 'client')
            ->where('is_active', '=', 1)
            ->select('id', 'name', 'email', 'type')
            ->orderBy('name', 'asc')
            ->get();

            // Ensure current user is included if not already
            $currentUserInList = $users->where('id', $currentUser->id)->first();
            if (!$currentUserInList) {
                $users = $users->push($currentUser);
                $users = $users->sortBy('name');
            }

            return response()->json([
                'success' => true,
                'users' => $users->values()
            ]);

        } catch (\Exception $e) {
            \Log::error('Error fetching filter users: ' . $e->getMessage(), [
                'user_id' => $currentUser->id ?? 'unknown',
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to fetch users',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get tags for filter dropdown
     */
    public function getFilterTags(Request $request)
    {
        // Enhanced authentication check
        if (!\Auth::check()) {
            \Log::warning('Filter tags request without authentication');
            return response()->json(['error' => __('User not authenticated.')], 401);
        }

        $currentUser = \Auth::user();
        if (!$currentUser) {
            \Log::warning('Auth check passed but user is null');
            return response()->json(['error' => __('User not authenticated.')], 401);
        }

        if (!$currentUser->can('manage lead')) {
            return response()->json(['error' => __('Permission Denied.')], 403);
        }

        try {
            // Get creator ID safely with null checks
            if ($currentUser->type == 'company' || $currentUser->type == 'super admin') {
                $creatorId = $currentUser->id;
            } else {
                $creatorId = $currentUser->created_by;
                if (!$creatorId) {
                    \Log::error('User has no created_by value', ['user_id' => $currentUser->id]);
                    return response()->json(['error' => 'Invalid user data'], 400);
                }
            }

            // Get all labels/tags from the company
            $tags = \App\Models\Label::where('created_by', $creatorId)
                ->select('id', 'name', 'color')
                ->orderBy('name', 'asc')
                ->get();

            return response()->json([
                'success' => true,
                'tags' => $tags
            ]);

        } catch (\Exception $e) {
            \Log::error('Error fetching filter tags: ' . $e->getMessage(), [
                'user_id' => $currentUser->id ?? 'unknown',
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to fetch tags',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get sources for filter dropdown
     */
    public function getFilterSources(Request $request)
    {
        // Enhanced authentication check
        if (!\Auth::check()) {
            \Log::warning('Filter sources request without authentication');
            return response()->json(['error' => __('User not authenticated.')], 401);
        }

        $currentUser = \Auth::user();
        if (!$currentUser) {
            \Log::warning('Auth check passed but user is null');
            return response()->json(['error' => __('User not authenticated.')], 401);
        }

        if (!$currentUser->can('manage lead')) {
            return response()->json(['error' => __('Permission Denied.')], 403);
        }

        try {
            // Get creator ID safely with null checks
            if ($currentUser->type == 'company' || $currentUser->type == 'super admin') {
                $creatorId = $currentUser->id;
            } else {
                $creatorId = $currentUser->created_by;
                if (!$creatorId) {
                    \Log::error('User has no created_by value', ['user_id' => $currentUser->id]);
                    return response()->json(['error' => 'Invalid user data'], 400);
                }
            }

            // Get all sources from the company
            $sources = \App\Models\Source::where('created_by', $creatorId)
                ->select('id', 'name')
                ->orderBy('name', 'asc')
                ->get();

            return response()->json([
                'success' => true,
                'sources' => $sources
            ]);

        } catch (\Exception $e) {
            \Log::error('Error fetching filter sources: ' . $e->getMessage(), [
                'user_id' => $currentUser->id ?? 'unknown',
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to fetch sources',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}

