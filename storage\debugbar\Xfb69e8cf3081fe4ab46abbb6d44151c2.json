{"__meta": {"id": "Xfb69e8cf3081fe4ab46abbb6d44151c2", "datetime": "2025-07-29 06:11:36", "utime": **********.810541, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.095037, "end": **********.81057, "duration": 0.7155330181121826, "duration_str": "716ms", "measures": [{"label": "Booting", "start": **********.095037, "relative_start": 0, "end": **********.720847, "relative_end": **********.720847, "duration": 0.625809907913208, "duration_str": "626ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.720866, "relative_start": 0.****************, "end": **********.810579, "relative_end": 9.059906005859375e-06, "duration": 0.*****************, "duration_str": "89.71ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "p89lkJvA8tU8k0TxpLbjJVXccgKASMRqgQX45FdF", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1605013489 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1605013489\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-59117005 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-59117005\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1971347008 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1971347008\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1910503554 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1910503554\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-386237342 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-386237342\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1737677002 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:11:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9ybWN1S2RXbXJXSGdnM0w0U1BSREE9PSIsInZhbHVlIjoiT3VOYkRRMjVqbnUvZFhCM05Scm1IS3N5SjM5c0hweDFyVERoWmxUSGJnTFBpS3dGek1tRkxUVTdGZDR1R2c1OUlla09Bajd5SlU4eDVhTmRFQzlWWUJKcFZVNXNpRExHc3EzYi85VGZJSlc1ay9FT2VTQ2ZEQmY2UVVjaDkra2NRaitnY284WUdtYmtIZGZ6a21zSFpvMEtLNzFFNkxOOFVXR2FXY1N5RWFVSklEM0JJYm9udmNUYmRVQXdhYmlZbXhjS2M0cEVSSFpiWGcrSW56anJGSkNreXJ4b2h2enFYcmpkMnpzVWxXRlVPYTRxSlhKenRaSUJkUVZ3MDhQd2QzdU9DbmVlYi9DZCtSMjNmTnpDc05pQXdtWnlrekw2a1N4ZHk4cUNZVUkwdjltVHRhSkdTSmszWkhSRFZHbmFtWnpkZmRtQkZCdGFXTDQ3NTFjR0xYdUp6ZkVPYXVnN1Z3NlhuYTA1WmJJdEpFaDR1T0pBdTVHTGNJdGo0UEFpbFpEZ3RtYldHeTNyTkIwUHdrM0RxcStNQnU2dWRJMWNFSnFDWlBZZDFYcmQvN01CbXRrYXpUTCsvWUpIcS82MC9nbXNNUlhFdjRSbmtxcjFuemdJZ3dlYWRycHhzQ2tyOXpleHlMcnV3b1RtczFDRE83V3NLR25RZ0VPRERUdUQiLCJtYWMiOiI1YmI3OTk2ZTY0NGRkZTYyODQ2ZjZhNWNjNzU0ZTI1NzVmODNhMzc4YTA3MmY5NWRjNjU2NmU5NGNkMzBlYWU5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:11:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlJicisxU3Q5a1J4ZWZaN0ZDNzdzV1E9PSIsInZhbHVlIjoiTFczV0J0ZU14UWdudy8ydGxDbEFybGFtYy85ZzhIdWIzSDJnRlRtTXZ3Y3VBM1RpcmhLVHBObGRNSEUwZTR5VmNDaTQxSUVnT3I3dUcweVhGQXBWSkZqSy9DM1RvWU5EcW5QUDdzaEFGWEprTHlXWUpBb3h5c3c0UXZRT3h6RWYrZk1ld2ZPR095V0xKRFlYUytyeVB3TXVjNFVxaForcWhQOFV3OFp5SjdET3g0bHZ6M2VQdzVNQzAzTW1BTk1qdXdGbEJJelFiMXFMeHBpc05aQmFlOU9FZXArLzRsVjRjd05NamRneGk0SmF2eUc4dVZNT0I3ZTkyaG1JalZKNGhLZ2pzU0gvcEtYZU9UbVFIbkZBcm0vam1sU3hEN2V2dS9UN0hoZlFmc0VPZjB5bEhhQmRkVlF4ZmFkQnJtN2R0b3Z0amRyQmV4Rm1ZaGs0ZCtwQW15dmxWRS9ueDZqQlRBY1RvclJEdFNaakJTNlQ4VVR5Q2NxREc4WVpCQlJmQ2dsZEVHbElMZDd3YTNLdE1OTFpnOWQzeGRiT1oyV1FQOU1JeWpnM2NrenVLUU1lcnlWbGlGUVdtV2xvUTVZTGhIN1c3UUpORjZzUVJxaEZ5akY5YjJvQ1hCV3ErN0xyYk04QWtxOGt1S1FCTUFtRHAzOE0zNzdKQzRHNXVKalYiLCJtYWMiOiJhZDU0MjViMmE3Zjk2OWUxYjdjNzM5NjRhNDI3NGM5MmU0NWNjOGQzYTY2ODI2M2Q1OTBkN2M5YTBmY2EzMzI3IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:11:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9ybWN1S2RXbXJXSGdnM0w0U1BSREE9PSIsInZhbHVlIjoiT3VOYkRRMjVqbnUvZFhCM05Scm1IS3N5SjM5c0hweDFyVERoWmxUSGJnTFBpS3dGek1tRkxUVTdGZDR1R2c1OUlla09Bajd5SlU4eDVhTmRFQzlWWUJKcFZVNXNpRExHc3EzYi85VGZJSlc1ay9FT2VTQ2ZEQmY2UVVjaDkra2NRaitnY284WUdtYmtIZGZ6a21zSFpvMEtLNzFFNkxOOFVXR2FXY1N5RWFVSklEM0JJYm9udmNUYmRVQXdhYmlZbXhjS2M0cEVSSFpiWGcrSW56anJGSkNreXJ4b2h2enFYcmpkMnpzVWxXRlVPYTRxSlhKenRaSUJkUVZ3MDhQd2QzdU9DbmVlYi9DZCtSMjNmTnpDc05pQXdtWnlrekw2a1N4ZHk4cUNZVUkwdjltVHRhSkdTSmszWkhSRFZHbmFtWnpkZmRtQkZCdGFXTDQ3NTFjR0xYdUp6ZkVPYXVnN1Z3NlhuYTA1WmJJdEpFaDR1T0pBdTVHTGNJdGo0UEFpbFpEZ3RtYldHeTNyTkIwUHdrM0RxcStNQnU2dWRJMWNFSnFDWlBZZDFYcmQvN01CbXRrYXpUTCsvWUpIcS82MC9nbXNNUlhFdjRSbmtxcjFuemdJZ3dlYWRycHhzQ2tyOXpleHlMcnV3b1RtczFDRE83V3NLR25RZ0VPRERUdUQiLCJtYWMiOiI1YmI3OTk2ZTY0NGRkZTYyODQ2ZjZhNWNjNzU0ZTI1NzVmODNhMzc4YTA3MmY5NWRjNjU2NmU5NGNkMzBlYWU5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:11:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlJicisxU3Q5a1J4ZWZaN0ZDNzdzV1E9PSIsInZhbHVlIjoiTFczV0J0ZU14UWdudy8ydGxDbEFybGFtYy85ZzhIdWIzSDJnRlRtTXZ3Y3VBM1RpcmhLVHBObGRNSEUwZTR5VmNDaTQxSUVnT3I3dUcweVhGQXBWSkZqSy9DM1RvWU5EcW5QUDdzaEFGWEprTHlXWUpBb3h5c3c0UXZRT3h6RWYrZk1ld2ZPR095V0xKRFlYUytyeVB3TXVjNFVxaForcWhQOFV3OFp5SjdET3g0bHZ6M2VQdzVNQzAzTW1BTk1qdXdGbEJJelFiMXFMeHBpc05aQmFlOU9FZXArLzRsVjRjd05NamRneGk0SmF2eUc4dVZNT0I3ZTkyaG1JalZKNGhLZ2pzU0gvcEtYZU9UbVFIbkZBcm0vam1sU3hEN2V2dS9UN0hoZlFmc0VPZjB5bEhhQmRkVlF4ZmFkQnJtN2R0b3Z0amRyQmV4Rm1ZaGs0ZCtwQW15dmxWRS9ueDZqQlRBY1RvclJEdFNaakJTNlQ4VVR5Q2NxREc4WVpCQlJmQ2dsZEVHbElMZDd3YTNLdE1OTFpnOWQzeGRiT1oyV1FQOU1JeWpnM2NrenVLUU1lcnlWbGlGUVdtV2xvUTVZTGhIN1c3UUpORjZzUVJxaEZ5akY5YjJvQ1hCV3ErN0xyYk04QWtxOGt1S1FCTUFtRHAzOE0zNzdKQzRHNXVKalYiLCJtYWMiOiJhZDU0MjViMmE3Zjk2OWUxYjdjNzM5NjRhNDI3NGM5MmU0NWNjOGQzYTY2ODI2M2Q1OTBkN2M5YTBmY2EzMzI3IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:11:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1737677002\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1061674499 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p89lkJvA8tU8k0TxpLbjJVXccgKASMRqgQX45FdF</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1061674499\", {\"maxDepth\":0})</script>\n"}}