{"__meta": {"id": "Xe17bc18a1c6f3796bc3ec2b27b793f8f", "datetime": "2025-07-29 05:54:21", "utime": **********.255167, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753768460.360596, "end": **********.255196, "duration": 0.8946001529693604, "duration_str": "895ms", "measures": [{"label": "Booting", "start": 1753768460.360596, "relative_start": 0, "end": **********.172556, "relative_end": **********.172556, "duration": 0.811959981918335, "duration_str": "812ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.172571, "relative_start": 0.****************, "end": **********.255208, "relative_end": 1.1920928955078125e-05, "duration": 0.*****************, "duration_str": "82.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "DyIpsSUvEuhnooL5ck3xAgpSuLkPr1RD5mg27cmS", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1816067211 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1816067211\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1897495208 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1897495208\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1502745316 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1502745316\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-55530312 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-55530312\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1042534288 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1042534288\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1075709755 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:54:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBORVVrSGc3SHU3eU1vWlRKZFN2Z0E9PSIsInZhbHVlIjoiNytwRHlwQkdGcHE2K2FDZkg1NkZ6VVByakoyQVJpNG1reHdsd2lvOGdHRDZiNXY5SjMrNXkvRGVReHV0MUR3V0R6OWxsOVV4S0cwTnNvT2JFckF1bGxYYUJ0Ulc2NjNLcnlBMWVnamhDeW0xRmFUdGhTV0RlMnVIQ3VvQ3ZJd01NQVRmM2d3OURuRlZRRVkxQzllZm1TRkpPbDk5WnhESzJvM3JFc0ZheTRtZWJrZ2xBMTRHZ1dmeFJ3bzU1b1U2QUo5VGh1MWJjYm1sK1V6ZWV2UnNZWHR0RWk4dmE1TnJhc3R4RXlTeUs2TUJrQ2dnYk1EejVIeFlaT2QrclJqTFBBcE5UeUs3K3FVSGVhQ3dUcTJOK25yQ3BZaVFNN2NDem1wV2x6Z0pDMnJmUjdCU3RpZGhKTzZWdnFpQStLK3QrZ1BJTkt6d2VoYkMyUitjYzRoS3FMUEJxK3hiWlBMeXRHNUgyZUcxY0NIMjlqQk1pWDhmNVNHcjhVOXl2TnUrZFB1WE1hYXNPYXljYnJZREprNU5zMVVia01NaTc1a2V5MlJLTy9PSngyVWY3VFpkNzU2a0g4UGNtOENCS0QyREp3UGJ1Tkc3U3dCL0VDM1dTRlZ5dThmSnpMQ0xFSytBZ0lhRWxQQ0FOZnBtZjllSFpZSHZsZTJWNmNYM0NodmkiLCJtYWMiOiI1YjRhMjk0OWY4NzU3NWUzOTFkOTY2YmZlOTg1YTZhODU4YWI1YTAyYTkwNmE2NTY2NzBkMjZkOWMxMjVmM2FiIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:54:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjhMaTJaQ2NUQjkzbW51a0NFbTNyMEE9PSIsInZhbHVlIjoieENkODkzNmhsRGlnV3E2YlNvanhyRnVlY05KanI5dVZTUlZJUTZQREt2VUx3K2FrcTNQdk9CYi9yelpQN0JuUllYai9kZTZZUzBOTjV6Uy9aV09WOG00Y3d4VFdtWnJjTEl5QkNwRW9GRFY3KzE3ak40VEswV0hDNTZNM0QzT3crSTFtQUc4YU9qREFTU3l0QW9kSFlMb1RXalp6QWpyN3BPUTNYVzBqSnVNeVJ2cFFoc2RZZGx3K2I1dHdxRmI5TGQyS3lidUFqL2lEK0l3MnhrcVlTbE1tZUY3UmljNDVNWUo5MHVXeFc4eTVweVNxTVdVY2ZoMDFUTVkwa0cvNkxBVVJCWm9jc3d5Nzh0VjR1SUo0b2N1cGpMakJ4RGpxTlhqdXlvb2FzZjVROXhOSEtOSnJOMlNNMjVNdHRPL1IrTGNhMGJabVNVZmplamxVSXd2UTJZbWJHMENzV0JCdk1TVnNvSkMvWEdwQTQwZVg5cVF6ZXZqZlRWbCsrVjRLZmNOYjJ4Q0w3NmFPQUtpaTBKT2Ywc0pvSHUySFNscjVBWnR3NDR5cVUxUEFxMXg2SmMzVFNuTVFmc1hndXV5UGZVUEJ2V1hLSm8wNUhZWGN4NHpvdnZ6NDU0elZ4bTNZR2wvU0pSa09VTk1VVlh6eUIvU1NHeTMvajdkTjROMUwiLCJtYWMiOiIxMTU5YzBmYzBkNjYzNzIyMmExOTg4NmQzYWE0YzQ4ODk3NjYxNDZiMDg5NDFjYzYzMzkyYTcyNTk5M2ViZTQxIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:54:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBORVVrSGc3SHU3eU1vWlRKZFN2Z0E9PSIsInZhbHVlIjoiNytwRHlwQkdGcHE2K2FDZkg1NkZ6VVByakoyQVJpNG1reHdsd2lvOGdHRDZiNXY5SjMrNXkvRGVReHV0MUR3V0R6OWxsOVV4S0cwTnNvT2JFckF1bGxYYUJ0Ulc2NjNLcnlBMWVnamhDeW0xRmFUdGhTV0RlMnVIQ3VvQ3ZJd01NQVRmM2d3OURuRlZRRVkxQzllZm1TRkpPbDk5WnhESzJvM3JFc0ZheTRtZWJrZ2xBMTRHZ1dmeFJ3bzU1b1U2QUo5VGh1MWJjYm1sK1V6ZWV2UnNZWHR0RWk4dmE1TnJhc3R4RXlTeUs2TUJrQ2dnYk1EejVIeFlaT2QrclJqTFBBcE5UeUs3K3FVSGVhQ3dUcTJOK25yQ3BZaVFNN2NDem1wV2x6Z0pDMnJmUjdCU3RpZGhKTzZWdnFpQStLK3QrZ1BJTkt6d2VoYkMyUitjYzRoS3FMUEJxK3hiWlBMeXRHNUgyZUcxY0NIMjlqQk1pWDhmNVNHcjhVOXl2TnUrZFB1WE1hYXNPYXljYnJZREprNU5zMVVia01NaTc1a2V5MlJLTy9PSngyVWY3VFpkNzU2a0g4UGNtOENCS0QyREp3UGJ1Tkc3U3dCL0VDM1dTRlZ5dThmSnpMQ0xFSytBZ0lhRWxQQ0FOZnBtZjllSFpZSHZsZTJWNmNYM0NodmkiLCJtYWMiOiI1YjRhMjk0OWY4NzU3NWUzOTFkOTY2YmZlOTg1YTZhODU4YWI1YTAyYTkwNmE2NTY2NzBkMjZkOWMxMjVmM2FiIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:54:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjhMaTJaQ2NUQjkzbW51a0NFbTNyMEE9PSIsInZhbHVlIjoieENkODkzNmhsRGlnV3E2YlNvanhyRnVlY05KanI5dVZTUlZJUTZQREt2VUx3K2FrcTNQdk9CYi9yelpQN0JuUllYai9kZTZZUzBOTjV6Uy9aV09WOG00Y3d4VFdtWnJjTEl5QkNwRW9GRFY3KzE3ak40VEswV0hDNTZNM0QzT3crSTFtQUc4YU9qREFTU3l0QW9kSFlMb1RXalp6QWpyN3BPUTNYVzBqSnVNeVJ2cFFoc2RZZGx3K2I1dHdxRmI5TGQyS3lidUFqL2lEK0l3MnhrcVlTbE1tZUY3UmljNDVNWUo5MHVXeFc4eTVweVNxTVdVY2ZoMDFUTVkwa0cvNkxBVVJCWm9jc3d5Nzh0VjR1SUo0b2N1cGpMakJ4RGpxTlhqdXlvb2FzZjVROXhOSEtOSnJOMlNNMjVNdHRPL1IrTGNhMGJabVNVZmplamxVSXd2UTJZbWJHMENzV0JCdk1TVnNvSkMvWEdwQTQwZVg5cVF6ZXZqZlRWbCsrVjRLZmNOYjJ4Q0w3NmFPQUtpaTBKT2Ywc0pvSHUySFNscjVBWnR3NDR5cVUxUEFxMXg2SmMzVFNuTVFmc1hndXV5UGZVUEJ2V1hLSm8wNUhZWGN4NHpvdnZ6NDU0elZ4bTNZR2wvU0pSa09VTk1VVlh6eUIvU1NHeTMvajdkTjROMUwiLCJtYWMiOiIxMTU5YzBmYzBkNjYzNzIyMmExOTg4NmQzYWE0YzQ4ODk3NjYxNDZiMDg5NDFjYzYzMzkyYTcyNTk5M2ViZTQxIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:54:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1075709755\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2000191587 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DyIpsSUvEuhnooL5ck3xAgpSuLkPr1RD5mg27cmS</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2000191587\", {\"maxDepth\":0})</script>\n"}}