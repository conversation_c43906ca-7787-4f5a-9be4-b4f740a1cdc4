{"__meta": {"id": "X8df3c59273b13464f4317c9b382479c2", "datetime": "2025-07-29 05:58:40", "utime": **********.895915, "method": "GET", "uri": "/contact-groups/available-contacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753768719.95003, "end": **********.895936, "duration": 0.9459059238433838, "duration_str": "946ms", "measures": [{"label": "Booting", "start": 1753768719.95003, "relative_start": 0, "end": **********.779778, "relative_end": **********.779778, "duration": 0.8297479152679443, "duration_str": "830ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.779792, "relative_start": 0.8297619819641113, "end": **********.895938, "relative_end": 1.9073486328125e-06, "duration": 0.11614584922790527, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46645160, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET contact-groups/available-contacts", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\ContactGroupController@getAvailableContacts", "namespace": null, "prefix": "", "where": [], "as": "contact-groups.available-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=221\" onclick=\"\">app/Http/Controllers/ContactGroupController.php:221-260</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.010119999999999999, "accumulated_duration_str": "10.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.850406, "duration": 0.007809999999999999, "duration_str": "7.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 77.174}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.874787, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 77.174, "width_percent": 9.684}, {"sql": "select `id`, `name`, `email`, `phone` from `leads` where `created_by` = 84 and `contact_group_id` is null and `is_active` = 1 and `is_deleted` = 0 order by `name` asc", "type": "query", "params": [], "bindings": ["84", "1", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ContactGroupController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactGroupController.php", "line": 231}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8802712, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "ContactGroupController.php:231", "source": "app/Http/Controllers/ContactGroupController.php:231", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=231", "ajax": false, "filename": "ContactGroupController.php", "line": "231"}, "connection": "omx_sass_systam_db", "start_percent": 86.858, "width_percent": 13.142}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contact-groups\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/contact-groups/available-contacts", "status_code": "<pre class=sf-dump id=sf-dump-608804709 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-608804709\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-155764375 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-155764375\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-91262151 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-91262151\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-70278346 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImdBeDd1TEdrSlgwcXVES2lCN0J0a2c9PSIsInZhbHVlIjoiaHBNTFNoN2dNeTNIS2x2OW84VGF5TnpBcmxLZzlOak82aXV6aW1KaVVTUkNDZmc2TFYrKzhveEkxamNwdnJFangvaUQ3VHhDMEZpQklLVVovQ2NBY0xRdmt1UVBheUJlSW5LbkQxQTBBeEpKRlJyNTN2OXJxSCsxemIvTm9DUGpNSUJlMWJXYUtxTzZoWkN6dFMzK1owY21KUk5EMVRWUGFRanI0VjZRMlhhVDNNK0ZYMHh4UzVOLzNYZ0Q2cEg5SjBqbGwzdWVDQmxFbVZJZlZpU1dKZ2ZEdnZoT2k5ZGlEMlBNVVBqZVNIL2tVOVdnNDdUZ0UzQjI2NFVLbU8zTEh4VlVSSjNwVjhXdjlmdW9ncjdXbVlPMFdpc0JXZzdJV1hYT294Rk9yOU4yV2JzbnBNd3JyN3ZUQ0ZtRG5Qa29mckV0M1ZtdGlvRTZXZDhGTXVNSlNRbDRTT1JtY25UUmJ4Ym4xVzNpcENoWkQrRldVUmg1VzF3cmozOS90SzNzRUQxbndQbWdJWVRYeVlCV1loMzVscUs5UVlrbTd5T1hRTk9aaHlYTkIvNWR2czBtbkp3MGovc3hWMC9Va005anR6NEQxc1l4dEpyNlphY1gwL2FsdXI4S0Y0RDl3VmpWOTVmZld1Ung5RmFOS1dnRHJ5dTFGb1RIYnFNaWpxb08iLCJtYWMiOiI2NDgzM2QwNjdlMDk2MTg3NTUyYTkwOGQ4YmRiZjI3NmQzMGJiY2YwNmVlMDgyMjE2OWQ3OTQ1NmYyMjQ3NThkIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im9uc0pkUjU2V29sU2F2Unp3TVphc1E9PSIsInZhbHVlIjoiVjNDSHJIU1RYRHhsWUlpbkFHcUtTOGU4OUdtamhvS05rdENXcGtrTnhHd0s4S244WTQ0Z291RDFYOHB6WDQ2dzJoRWpyc09YRStRbGFYbXMrMzhoc3U4cEhUWkZwbEh6di9kRFZ4UDdmZG1ESmtMMlhqUXgySitxalRRdTdpUGo2M0Z4TEwwTnk2NGc2RFBoRjZvanowc1JwQWZFZVhKbWwveVdsa2lsN2czTTBhajhZbEdOL3NuQkZLdzNGMFJCUXYzRzdUcys0K1pRL3FXQSs0enBUUHQvWGQ5SVhOZWpOeVRvb0p2R1hzSEdJOThrYktYeW1PNSszMmFZVk9wQk5XK2ljTGhDYUw2YnkzQ2NFY1pwMWRobEpDYkFFcTEwQ2VzU0pqVGNXbWwvQ3NERDBVQ240VHBvbnc2SjZlRU1GVTRQMXJkUXdNb3F1OEVQanlrUFBVQUtOWVlNdTVyZWFEUXY5VWU4Z1d0clluYXlxSk5TeVZKd3BqYTdOc2lVcmpJVGVLWm9mbVZFNjRReDVwTTlLQmFEc3E5dHEwb3lCVm1sN0o5aVVBOEdzRkc0ZG9Kc2FobTFQYWNvMGdXWDJ2TnlKbXZrdlo2bnI2ZTd1eUdiZ3pwb09VaGo2RWZSV01FWXUxWGJzQ2tFaDNId040TlZOK05OU2JJT0ZUUXQiLCJtYWMiOiI0ZDRiMjNkMTU0OWU4YjVmMmE3MjdhMDA0NjgxNmVhYWFjYTljZWVlNjE3NDdmYzJlYzEwZjNiNGUxNDFkMjgzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-70278346\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2031340871 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2031340871\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1183412489 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:58:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5vQWkyVHRJOFJ5Rjd0V3BVSzhLaVE9PSIsInZhbHVlIjoiSldCeFl5VjIyQlZRNVpBZDJSM0JNVUs1dU1MeXpvdGdHV1RQblptWFFzR0RGSGh3allER1pCbFlxaVl5MnVwY1IvbEY0R09kZC9wU29WbDNmTEZGUGFlZFRjSnJaRnBDdHZMc2RHZXJlOUVmcVI4QXl3YktZUGdZVFJRVTdhLzZWRUxRbGFjandWTFQyTURGdG5GU2hhcExoWWpTQUFzSGRZWm9ZVVZnUVdtcVlFb2RUS0tBYlRLNmVidUVBQU12anVoNVFmQjZrSFRXZTBWS2VubGJxMStrT2tnaUFmKzhXY01WYVFvaGFUKzFqQ1VGOVJoakxzSWxqNGF1blhaSjZrSThlVFpOVXB5K28yb2ZMQWNDeGF0RzZteDFFcnQ0MW5rejZMSkx6Q1BLTjJaT2dSYWN0cjR6K0NjWWV3SjFQam5DUGh3Tjl4SkpWT2wvc0NQc3ZsMTBQMW5DSUlpVXp2TW5YblFUdzlnNFNNaXg5am1paUdzUXhUMXNEOTNUUzJxUjJLeHMxWHpITzFwdFBJU1ZBVWNOOFJGaXNCUExYRERkTDZjM0dJbnVlTnJOWElVNkRxTzJ2dG9CZTh1N08rZ0krbWdQYUlYNTV2dEpYTU1vWDRSdjdtbTNZSEZ1eW5XM3I0NmkzNHpTcGRMS0QyYTFzR1dJMERiN2FVK2QiLCJtYWMiOiJkYjc4YTVkYzAwZTg4NGQxY2VjMmNiMWRlYzE5MGE5OTM4OGRkZTE5NjIwNjJiNzczNjQ0NTJjM2YzYjQ2MzdlIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:58:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ii9mL3NhemR6TFdrVm9GMkZuYVhmRUE9PSIsInZhbHVlIjoiSVlucTFoaGZKMXJpY2JDcTgrOUhhQ3dlVHpqaVRsMnRETHJFSmZ0MGg0Wnp3WFpGblQ4SUtSTFdQSHQ3V250MktvUHI5TVBwUmVTSXVhVWV0ZmFMSjA2RnN2VnhhYlplbytRd1JSTWNiS0YybEFWaFRiaW1WN0NweXdLNXlMeHo3UjdBa2hLQUg0WVdjWHBLcUMxaDliWDFhN2lEVkhnRmwvbDR3T3hnSU9JcENNaFJoSzh6SmpxQWMzUll0aFhQOEg0M3huZERNQzZZYVJuTkRmckI0UXpia1dmSTJKMzNYVlZNb0VHd28rYzdaT2FoUGhENlRUZTJZUi9BRVlxQW5JSDNIaE9DOGVjZndjVzBuckhWNzNBbVFUazEyZ0tna2tZellWUXZ2VHFtL1hrSE1FOC9WUWlHRUt2b0VPa1pyRjlNdUJnSjlmd01WY2xvV056N1FrdDRBYlVOak5jYXF5MnRLaGU3OTZpUEkwNlFwUjVvYnA4NFExL3hWMDJ5RGZYcXNsenhpZ3JZYVJJYndjcENzTmhBaE5iMGJxQUxKWHUza0JQS0dWV3JaWWhidndhUEVvckk2SnltTlpkZUtXT1h5Zld5VmN6SmtrVHJac0ZiZDdqd0hzaE1xdUpUdzd3NUhkUG9kSzhhbHJjelFZK3U5T0Z0bFJqNWFyVi8iLCJtYWMiOiIwM2Y0NGZmMzQ2ZWI5ZDVhMWE2OGVhY2ZmZjliN2JkMjY2OTA3ODUyYmFjYzRhYzg3YzM5ODk0NDVhMjU1MmIwIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:58:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5vQWkyVHRJOFJ5Rjd0V3BVSzhLaVE9PSIsInZhbHVlIjoiSldCeFl5VjIyQlZRNVpBZDJSM0JNVUs1dU1MeXpvdGdHV1RQblptWFFzR0RGSGh3allER1pCbFlxaVl5MnVwY1IvbEY0R09kZC9wU29WbDNmTEZGUGFlZFRjSnJaRnBDdHZMc2RHZXJlOUVmcVI4QXl3YktZUGdZVFJRVTdhLzZWRUxRbGFjandWTFQyTURGdG5GU2hhcExoWWpTQUFzSGRZWm9ZVVZnUVdtcVlFb2RUS0tBYlRLNmVidUVBQU12anVoNVFmQjZrSFRXZTBWS2VubGJxMStrT2tnaUFmKzhXY01WYVFvaGFUKzFqQ1VGOVJoakxzSWxqNGF1blhaSjZrSThlVFpOVXB5K28yb2ZMQWNDeGF0RzZteDFFcnQ0MW5rejZMSkx6Q1BLTjJaT2dSYWN0cjR6K0NjWWV3SjFQam5DUGh3Tjl4SkpWT2wvc0NQc3ZsMTBQMW5DSUlpVXp2TW5YblFUdzlnNFNNaXg5am1paUdzUXhUMXNEOTNUUzJxUjJLeHMxWHpITzFwdFBJU1ZBVWNOOFJGaXNCUExYRERkTDZjM0dJbnVlTnJOWElVNkRxTzJ2dG9CZTh1N08rZ0krbWdQYUlYNTV2dEpYTU1vWDRSdjdtbTNZSEZ1eW5XM3I0NmkzNHpTcGRMS0QyYTFzR1dJMERiN2FVK2QiLCJtYWMiOiJkYjc4YTVkYzAwZTg4NGQxY2VjMmNiMWRlYzE5MGE5OTM4OGRkZTE5NjIwNjJiNzczNjQ0NTJjM2YzYjQ2MzdlIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:58:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ii9mL3NhemR6TFdrVm9GMkZuYVhmRUE9PSIsInZhbHVlIjoiSVlucTFoaGZKMXJpY2JDcTgrOUhhQ3dlVHpqaVRsMnRETHJFSmZ0MGg0Wnp3WFpGblQ4SUtSTFdQSHQ3V250MktvUHI5TVBwUmVTSXVhVWV0ZmFMSjA2RnN2VnhhYlplbytRd1JSTWNiS0YybEFWaFRiaW1WN0NweXdLNXlMeHo3UjdBa2hLQUg0WVdjWHBLcUMxaDliWDFhN2lEVkhnRmwvbDR3T3hnSU9JcENNaFJoSzh6SmpxQWMzUll0aFhQOEg0M3huZERNQzZZYVJuTkRmckI0UXpia1dmSTJKMzNYVlZNb0VHd28rYzdaT2FoUGhENlRUZTJZUi9BRVlxQW5JSDNIaE9DOGVjZndjVzBuckhWNzNBbVFUazEyZ0tna2tZellWUXZ2VHFtL1hrSE1FOC9WUWlHRUt2b0VPa1pyRjlNdUJnSjlmd01WY2xvV056N1FrdDRBYlVOak5jYXF5MnRLaGU3OTZpUEkwNlFwUjVvYnA4NFExL3hWMDJ5RGZYcXNsenhpZ3JZYVJJYndjcENzTmhBaE5iMGJxQUxKWHUza0JQS0dWV3JaWWhidndhUEVvckk2SnltTlpkZUtXT1h5Zld5VmN6SmtrVHJac0ZiZDdqd0hzaE1xdUpUdzd3NUhkUG9kSzhhbHJjelFZK3U5T0Z0bFJqNWFyVi8iLCJtYWMiOiIwM2Y0NGZmMzQ2ZWI5ZDVhMWE2OGVhY2ZmZjliN2JkMjY2OTA3ODUyYmFjYzRhYzg3YzM5ODk0NDVhMjU1MmIwIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:58:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1183412489\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2131302623 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2131302623\", {\"maxDepth\":0})</script>\n"}}