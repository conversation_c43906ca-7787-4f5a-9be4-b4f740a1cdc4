{"__meta": {"id": "Xa3eb5591a5af935c341e24e2262fe69b", "datetime": "2025-07-29 05:54:47", "utime": **********.767943, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753768486.889248, "end": **********.767985, "duration": 0.878737211227417, "duration_str": "879ms", "measures": [{"label": "Booting", "start": 1753768486.889248, "relative_start": 0, "end": **********.682614, "relative_end": **********.682614, "duration": 0.7933661937713623, "duration_str": "793ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.682645, "relative_start": 0.****************, "end": **********.767995, "relative_end": 1.0013580322265625e-05, "duration": 0.*****************, "duration_str": "85.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TxYw7QsOwEua1j5rBR1n3akazlRjJzjvl7qmMlq6", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1568087559 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1568087559\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1009348051 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1009348051\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-388502858 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-388502858\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-614521874 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-614521874\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-243950493 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-243950493\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-560954686 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:54:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBRTDR6c3pScHJFZDJmQ0pMNmtvOUE9PSIsInZhbHVlIjoiRCtybnRqZnVyb3J5dHVsa2dpMGZUZ1d1ZUNSZ1Z0akkvSXloM3JaOEEwc0RobHRDVGpKQnE3Zk1hMTVyZ29KcGJWSThhZ25GV3RZNTlhcTR6NTBhVzNvcXBtdU5nSmVYdE8zSTZRcTRNTzlXbmlkUUh0SklhZ1ZwbjVxbzZaU3ZCRFhKUXYxRUVqMEppU042MmJUY2VMZG8yVFVNZGpLKzFyM2ZSWjh4OVRxUXlCN0NPZmVYTW95dVFIMDh4K2ZQQ0dVZVR2YUxzYVMxRzUxNEFDcWVXajZkL2lHUU9OTEp3VC9YMFI5M3lzMFhQQ1lSbDlidnJIWWJQSjVBWVNmMEQ2YUpZazIrU0pCbjBXeStPQU9Ia0V6OU45Y09WSkZpeEs2NTlrdXg0NzZwYkhMN1JMSkNwTnVxVFdOS1kyL1FzVVRDdy9hVS96SXUwaEtGT25wazJQSnVPV1FsbnY0SzhNUnF0NkNHUzl6bFdkbVBOd0gxSlN0d1FKQ1ZLTDZqYng5OVJ1ZEZneVc2VmlMS3lxNC9QWFZ6dEJyL3JlSU9PS3dTa091WGdoVFlUWVVjSCtDRmhnT2U5K2FvaSt0TWRWVk5Pd2pKSHJyWW9TZktXNmh0MzNOSGFNbjY5Y3FjVFJ1S25vWGpQdWxMZ1JtME1DWHpXZTlvZGpoNkN0SDYiLCJtYWMiOiI5OGI5NTQ0YzdiNTRkNzZjYjVlMmZiMThmZjcwOTk3YzkxYTJlMDc0ZDQ1MDljMTE2YTcxY2I1MjkyYjc4Yzc2IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:54:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InRld09YYkNvZmZTTVJHd0dRYlVBNmc9PSIsInZhbHVlIjoiUDNrRW9iek9JN1B0M0ZGY0kyQWJBQ1kvVVF5cXplUzI5Qzh3MVN6a0tpc052cmVQa3Z1WEZoKzZsRFVTR1RyRk9TbnV6K1I3djVSVjBDdXhXK3d2eCtCbDVRRDh2OTZBdHlSUDdleEFveUcwK1NzdnJNZCtjWGxxT0Z0VzYxNlpRZVNOeVVyUkhiVmdhOTd2Zk92a3N3REZ1VjdacTI2cXJqMHAvMlNXaDhDbEZIT0w5SVZEZi9FTjhhck9xaFhPdWZDeFVoUktUcDJtR3ZKb25vR0tqTXdJYi9iNC9YN3UrVElVS0lDcDJiUFdMdU9UREpXaEwxY2kvNFA2Vm5UTmRQRVpOVkt3dGR0aWNFK3VTZm1Ocm55VG1yZzd4Y2JyWkUvL0d0RitwOVlJRGZnZlZ6RElYT3phVCtvOVBONmNIdG5rZXRlRjFWQlkzZm4wUGIrK2REWGd4VjA3RWlrNGNMLzgxa1ZPaTNqazNkS093SGpia1FjWmFRdTdZRm9aemdzMUJJQVlYNzdBV25CaGxuR3RaS2pSQ0o2ZHd4R2J2d1BDZmt1Y0tDWmp0RnREUzgwODNySDZ6S3g2ZGMwY1ZCRFJqOGgxeDkwZlVwU0hNSGxvZkhzUjE5a3BNTGNhakhCVHZPTmRDWXAwYmltd0RYUzBHby96WHN3SjBHQ0kiLCJtYWMiOiI4MmQzNWIzNzg3MzFlYzJhMDViNjIzMTVjNzNmNWY2MGFlOTIzNDY0ZWU4NDZkODc1OTIyZjUyN2IyMWIxNjMxIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:54:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBRTDR6c3pScHJFZDJmQ0pMNmtvOUE9PSIsInZhbHVlIjoiRCtybnRqZnVyb3J5dHVsa2dpMGZUZ1d1ZUNSZ1Z0akkvSXloM3JaOEEwc0RobHRDVGpKQnE3Zk1hMTVyZ29KcGJWSThhZ25GV3RZNTlhcTR6NTBhVzNvcXBtdU5nSmVYdE8zSTZRcTRNTzlXbmlkUUh0SklhZ1ZwbjVxbzZaU3ZCRFhKUXYxRUVqMEppU042MmJUY2VMZG8yVFVNZGpLKzFyM2ZSWjh4OVRxUXlCN0NPZmVYTW95dVFIMDh4K2ZQQ0dVZVR2YUxzYVMxRzUxNEFDcWVXajZkL2lHUU9OTEp3VC9YMFI5M3lzMFhQQ1lSbDlidnJIWWJQSjVBWVNmMEQ2YUpZazIrU0pCbjBXeStPQU9Ia0V6OU45Y09WSkZpeEs2NTlrdXg0NzZwYkhMN1JMSkNwTnVxVFdOS1kyL1FzVVRDdy9hVS96SXUwaEtGT25wazJQSnVPV1FsbnY0SzhNUnF0NkNHUzl6bFdkbVBOd0gxSlN0d1FKQ1ZLTDZqYng5OVJ1ZEZneVc2VmlMS3lxNC9QWFZ6dEJyL3JlSU9PS3dTa091WGdoVFlUWVVjSCtDRmhnT2U5K2FvaSt0TWRWVk5Pd2pKSHJyWW9TZktXNmh0MzNOSGFNbjY5Y3FjVFJ1S25vWGpQdWxMZ1JtME1DWHpXZTlvZGpoNkN0SDYiLCJtYWMiOiI5OGI5NTQ0YzdiNTRkNzZjYjVlMmZiMThmZjcwOTk3YzkxYTJlMDc0ZDQ1MDljMTE2YTcxY2I1MjkyYjc4Yzc2IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:54:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InRld09YYkNvZmZTTVJHd0dRYlVBNmc9PSIsInZhbHVlIjoiUDNrRW9iek9JN1B0M0ZGY0kyQWJBQ1kvVVF5cXplUzI5Qzh3MVN6a0tpc052cmVQa3Z1WEZoKzZsRFVTR1RyRk9TbnV6K1I3djVSVjBDdXhXK3d2eCtCbDVRRDh2OTZBdHlSUDdleEFveUcwK1NzdnJNZCtjWGxxT0Z0VzYxNlpRZVNOeVVyUkhiVmdhOTd2Zk92a3N3REZ1VjdacTI2cXJqMHAvMlNXaDhDbEZIT0w5SVZEZi9FTjhhck9xaFhPdWZDeFVoUktUcDJtR3ZKb25vR0tqTXdJYi9iNC9YN3UrVElVS0lDcDJiUFdMdU9UREpXaEwxY2kvNFA2Vm5UTmRQRVpOVkt3dGR0aWNFK3VTZm1Ocm55VG1yZzd4Y2JyWkUvL0d0RitwOVlJRGZnZlZ6RElYT3phVCtvOVBONmNIdG5rZXRlRjFWQlkzZm4wUGIrK2REWGd4VjA3RWlrNGNMLzgxa1ZPaTNqazNkS093SGpia1FjWmFRdTdZRm9aemdzMUJJQVlYNzdBV25CaGxuR3RaS2pSQ0o2ZHd4R2J2d1BDZmt1Y0tDWmp0RnREUzgwODNySDZ6S3g2ZGMwY1ZCRFJqOGgxeDkwZlVwU0hNSGxvZkhzUjE5a3BNTGNhakhCVHZPTmRDWXAwYmltd0RYUzBHby96WHN3SjBHQ0kiLCJtYWMiOiI4MmQzNWIzNzg3MzFlYzJhMDViNjIzMTVjNzNmNWY2MGFlOTIzNDY0ZWU4NDZkODc1OTIyZjUyN2IyMWIxNjMxIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:54:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-560954686\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1014595649 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TxYw7QsOwEua1j5rBR1n3akazlRjJzjvl7qmMlq6</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1014595649\", {\"maxDepth\":0})</script>\n"}}