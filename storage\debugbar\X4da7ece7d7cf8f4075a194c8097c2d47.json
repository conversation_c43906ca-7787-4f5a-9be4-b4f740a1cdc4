{"__meta": {"id": "X4da7ece7d7cf8f4075a194c8097c2d47", "datetime": "2025-07-29 06:09:13", "utime": **********.696524, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753769352.717271, "end": **********.696553, "duration": 0.9792819023132324, "duration_str": "979ms", "measures": [{"label": "Booting", "start": 1753769352.717271, "relative_start": 0, "end": **********.60919, "relative_end": **********.60919, "duration": 0.8919188976287842, "duration_str": "892ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.609213, "relative_start": 0.***************, "end": **********.696563, "relative_end": 1.0013580322265625e-05, "duration": 0.*****************, "duration_str": "87.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9hw2NFtMUkFrE4sFnLZ77VloPYZT8McjftJdTs88", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-234540047 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-234540047\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1513028971 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1513028971\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1601013795 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1601013795\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-499291467 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-499291467\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1899923632 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1899923632\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1723311245 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:09:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhlUGlHYlFzYjlBME1qOHg2TUg0d3c9PSIsInZhbHVlIjoiNnJ0Qkk5L1JVVVYwTWJ0S2QxUEE3OVg5U0tHdUtFMTdtUTVqcWNNYlhBdkxlbkFzaFM0aFh2c3Zodi9YOXJ5dlRNWVZYTWw1bElLNTl2N2FxZXNZTk1rRlVKS1FZV01Hd0RiUGhwbmdvVGxzMUNJelRpZlNzV0JJNzBmY3NqYWhzMUcyRkpTSjY4S3VzNnoyKytPQzBydVU1MU5TcmtZaWV3d3o5aTdyZ2tGVy9xcjI4VXdibjZzTXVHdVdianQ1V0liSWxEbk9kTjBkNVJSaUdDdnZwd2tnOER6MWpEa1kyKzk2cnR4OVJaV0d3Q3Arc3FWR0NnNEhsbnNRSmR2ZTFFLzFxZ3BrTlRaM0ZZL0hweTZXditLd0N2QktxcWNqcHh6SldVWG90T2lsSTBnVDEvSmpmdVpnV29DeHIranJXMk1UUWh4V3A3cHhWb3gwWEZyNjRWdnJKU2pvNzhHdE5iSE5QRU5ZYldzcHhjTDZoY1dNTGlCSHBsZjk3OTdvWEVxL3Nld1dlK2dUQ0piOGNwNjMyellhWWkwZlhhVVZtRkFTNWJNbUxqYVdBdGhNVUJzUTNleGNQci9SclNvanRmZ0V0a015cEhuMVE2aVlqQ1Z5Z2Q3T0xpdEk2b3kxVEROVGhsT3NRVktEMmdZNkRNMUoxTlNhRGFPeTNjdzgiLCJtYWMiOiI0MTU5MDI5ODczNzdmZDY2Y2RjYzg4MTBmYTRiOGFlYmUyOTBkMmJjZWEyYTYzNDMwOGMwZDk4NDJlZWU0NmU5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:09:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjNEaFJlc0s3S3VmcUpWLy9CUkZCd1E9PSIsInZhbHVlIjoia20zU1hVemRjWWRzdE44MktsTUZmdzAwbDVMOXpYanJleEhKNUU0UGJycVJqVWxLSVZmYnJ6RmpmOGg1TEhrKzRzclJKRGNVbmZyc2twTUJLeGdYNlV4bnVBS2x5MUJpcHZxS1hmV3V3a29GVkdlOS92cEtWUVUvZFExWGhOVjdQaHczQ0cwdWhTbllldTBkdjZ4alFRUGVReWs3dERQalIwTnNPNTJlbkU4VUx2bjc1YU50L0lUaG82Znl4akhMMGU5NCsrNXRIZnhEUXptM3c3SVhoTXdlRmpnTWVjeFkxVHJWZHNLTEZsd2FZT09yeGpSMFdwL0VsNTAwdDREek1BRlRzb2tuOW9zWWxLdDhGdDE1Q1h0Ui9yQ2NUUElYRDBneG0xRkp5cmtwN2YrYjNuY2FJNzJWcDhOQUlnNEw4ZEJqMEJ1bGY5TWZEaUI3ZG83M0srRkxSVnJKbEFHS0ovY0JVUTYvblNUNnhHK1Y5VUhQbUxQR2lKajVpUjAvUXd5b013b1dmK25hS0NYZGVqK0JnMTlYaTRJVWZheU5xQWhyWDcwVUVyblBmR2dlcDZQVm9Ua2dCZjZDWUZDVEEvNjhUZ09rZ1Rybkh5dmI3d1pXT1V0RlZYS3R4cEdWaGNqdE1DTi9yREorVUlXNUFKbTFEYnNZR0ZqQnNpUDciLCJtYWMiOiI2NzM1YmQ0NGJlOTI5MzcxZGJmZjZmYTgzN2IzNjY3MzllMGYxNGJhODgwOGI2ZDE1YmQ4NWRhZDQzMjdjZDQ0IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:09:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhlUGlHYlFzYjlBME1qOHg2TUg0d3c9PSIsInZhbHVlIjoiNnJ0Qkk5L1JVVVYwTWJ0S2QxUEE3OVg5U0tHdUtFMTdtUTVqcWNNYlhBdkxlbkFzaFM0aFh2c3Zodi9YOXJ5dlRNWVZYTWw1bElLNTl2N2FxZXNZTk1rRlVKS1FZV01Hd0RiUGhwbmdvVGxzMUNJelRpZlNzV0JJNzBmY3NqYWhzMUcyRkpTSjY4S3VzNnoyKytPQzBydVU1MU5TcmtZaWV3d3o5aTdyZ2tGVy9xcjI4VXdibjZzTXVHdVdianQ1V0liSWxEbk9kTjBkNVJSaUdDdnZwd2tnOER6MWpEa1kyKzk2cnR4OVJaV0d3Q3Arc3FWR0NnNEhsbnNRSmR2ZTFFLzFxZ3BrTlRaM0ZZL0hweTZXditLd0N2QktxcWNqcHh6SldVWG90T2lsSTBnVDEvSmpmdVpnV29DeHIranJXMk1UUWh4V3A3cHhWb3gwWEZyNjRWdnJKU2pvNzhHdE5iSE5QRU5ZYldzcHhjTDZoY1dNTGlCSHBsZjk3OTdvWEVxL3Nld1dlK2dUQ0piOGNwNjMyellhWWkwZlhhVVZtRkFTNWJNbUxqYVdBdGhNVUJzUTNleGNQci9SclNvanRmZ0V0a015cEhuMVE2aVlqQ1Z5Z2Q3T0xpdEk2b3kxVEROVGhsT3NRVktEMmdZNkRNMUoxTlNhRGFPeTNjdzgiLCJtYWMiOiI0MTU5MDI5ODczNzdmZDY2Y2RjYzg4MTBmYTRiOGFlYmUyOTBkMmJjZWEyYTYzNDMwOGMwZDk4NDJlZWU0NmU5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:09:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjNEaFJlc0s3S3VmcUpWLy9CUkZCd1E9PSIsInZhbHVlIjoia20zU1hVemRjWWRzdE44MktsTUZmdzAwbDVMOXpYanJleEhKNUU0UGJycVJqVWxLSVZmYnJ6RmpmOGg1TEhrKzRzclJKRGNVbmZyc2twTUJLeGdYNlV4bnVBS2x5MUJpcHZxS1hmV3V3a29GVkdlOS92cEtWUVUvZFExWGhOVjdQaHczQ0cwdWhTbllldTBkdjZ4alFRUGVReWs3dERQalIwTnNPNTJlbkU4VUx2bjc1YU50L0lUaG82Znl4akhMMGU5NCsrNXRIZnhEUXptM3c3SVhoTXdlRmpnTWVjeFkxVHJWZHNLTEZsd2FZT09yeGpSMFdwL0VsNTAwdDREek1BRlRzb2tuOW9zWWxLdDhGdDE1Q1h0Ui9yQ2NUUElYRDBneG0xRkp5cmtwN2YrYjNuY2FJNzJWcDhOQUlnNEw4ZEJqMEJ1bGY5TWZEaUI3ZG83M0srRkxSVnJKbEFHS0ovY0JVUTYvblNUNnhHK1Y5VUhQbUxQR2lKajVpUjAvUXd5b013b1dmK25hS0NYZGVqK0JnMTlYaTRJVWZheU5xQWhyWDcwVUVyblBmR2dlcDZQVm9Ua2dCZjZDWUZDVEEvNjhUZ09rZ1Rybkh5dmI3d1pXT1V0RlZYS3R4cEdWaGNqdE1DTi9yREorVUlXNUFKbTFEYnNZR0ZqQnNpUDciLCJtYWMiOiI2NzM1YmQ0NGJlOTI5MzcxZGJmZjZmYTgzN2IzNjY3MzllMGYxNGJhODgwOGI2ZDE1YmQ4NWRhZDQzMjdjZDQ0IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:09:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1723311245\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-193381008 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9hw2NFtMUkFrE4sFnLZ77VloPYZT8McjftJdTs88</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-193381008\", {\"maxDepth\":0})</script>\n"}}